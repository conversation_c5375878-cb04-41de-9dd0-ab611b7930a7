/**
 * @swagger
 * openapi: 3.0.0
 * info:
 *   title: Oroi Backend API
 *   description: API documentation for the Oroi language learning platform
 *   version: 1.0.0
 *   contact:
 *     email: <EMAIL>
 *
 * servers:
 *   - url: /api/v1
 *     description: API v1
 *
 * components:
 *   securitySchemes:
 *     bearerAuth:
 *       type: http
 *       scheme: bearer
 *       bearerFormat: JWT
 *
 *   schemas:
 *     User:
 *       type: object
 *       required:
 *         - username
 *         - email
 *         - role
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           description: The auto-generated id of the user
 *         username:
 *           type: string
 *           description: The username of the user
 *         email:
 *           type: string
 *           format: email
 *           description: The email of the user
 *         role:
 *           type: string
 *           description: The role of the user (USER, ADMIN, SUPERADMIN)
 *         uid:
 *           type: string
 *           description: External user ID (for social auth)
 *         profilePic:
 *           type: string
 *           description: URL to the user's profile picture
 *         FCM:
 *           type: string
 *           description: Firebase Cloud Messaging token
 *         isBlocked:
 *           type: boolean
 *           description: Whether the user is blocked
 *         isPremium:
 *           type: boolean
 *           description: Whether the user has premium access
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *         deletedAt:
 *           type: string
 *           format: date-time
 *
 *     Level:
 *       type: object
 *       required:
 *         - name
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         name:
 *           type: string
 *         description:
 *           type: string
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *         deletedAt:
 *           type: string
 *           format: date-time
 *
 *     Language:
 *       type: object
 *       required:
 *         - name
 *         - code
 *         - flag
 *         - englishName
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         name:
 *           type: string
 *         code:
 *           type: string
 *         flag:
 *           type: string
 *         englishName:
 *           type: string
 *         isActive:
 *           type: boolean
 *         isPremium:
 *           type: boolean
 *         pronunciation:
 *           type: string
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *         deletedAt:
 *           type: string
 *           format: date-time
 *
 *     Course:
 *       type: object
 *       required:
 *         - name
 *         - languageId
 *         - levelId
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         name:
 *           type: string
 *         description:
 *           type: string
 *         image:
 *           type: string
 *         isActive:
 *           type: boolean
 *         isPremium:
 *           type: boolean
 *         languageId:
 *           type: string
 *           format: uuid
 *         levelId:
 *           type: string
 *           format: uuid
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *         deletedAt:
 *           type: string
 *           format: date-time
 *
 *     Flashcard:
 *       type: object
 *       required:
 *         - word
 *         - pronunciation
 *         - languageId
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         isActive:
 *           type: boolean
 *         word:
 *           type: string
 *         pronunciation:
 *           type: string
 *         meaning:
 *           type: string
 *         example:
 *           type: string
 *         image:
 *           type: string
 *         audio:
 *           type: string
 *         languageId:
 *           type: string
 *           format: uuid
 *         proposeCardId:
 *           type: string
 *           format: uuid
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *         deletedAt:
 *           type: string
 *           format: date-time
 *
 *     Quiz:
 *       type: object
 *       required:
 *         - name
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         name:
 *           type: string
 *         description:
 *           type: string
 *         isActive:
 *           type: boolean
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *         deletedAt:
 *           type: string
 *           format: date-time
 *
 *     Question:
 *       type: object
 *       required:
 *         - question
 *         - questionType
 *         - quizId
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         question:
 *           type: string
 *         questionType:
 *           type: string
 *           enum: [multiple-choice, true-false, fill-in-the-blank]
 *         explanation:
 *           type: string
 *         isActive:
 *           type: boolean
 *         quizId:
 *           type: string
 *           format: uuid
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *         deletedAt:
 *           type: string
 *           format: date-time
 *
 *     Group:
 *       type: object
 *       required:
 *         - name
 *         - languageId
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         name:
 *           type: string
 *         description:
 *           type: string
 *         image:
 *           type: string
 *         isActive:
 *           type: boolean
 *         isPremium:
 *           type: boolean
 *         languageId:
 *           type: string
 *           format: uuid
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *         deletedAt:
 *           type: string
 *           format: date-time
 *
 *     Support:
 *       type: object
 *       required:
 *         - name
 *         - subject
 *         - description
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         name:
 *           type: string
 *         email:
 *           type: string
 *           format: email
 *         subject:
 *           type: string
 *         description:
 *           type: string
 *         status:
 *           type: string
 *           enum: [open, in-progress, closed]
 *         priority:
 *           type: string
 *           enum: [low, medium, high]
 *         resolution:
 *           type: string
 *         userId:
 *           type: string
 *           format: uuid
 *         assignedTo:
 *           type: string
 *           format: uuid
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *         deletedAt:
 *           type: string
 *           format: date-time
 *
 *     Notification:
 *       type: object
 *       required:
 *         - title
 *         - body
 *         - topic
 *         - click_action
 *         - userId
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         title:
 *           type: string
 *         body:
 *           type: string
 *         topic:
 *           type: string
 *         click_action:
 *           type: string
 *         clicks:
 *           type: integer
 *         schedule:
 *           type: string
 *           format: date-time
 *         userId:
 *           type: string
 *           format: uuid
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *
 *   parameters:
 *     userId:
 *       in: path
 *       name: id
 *       required: true
 *       schema:
 *         type: string
 *         format: uuid
 *       description: The user ID
 *
 *     levelId:
 *       in: path
 *       name: id
 *       required: true
 *       schema:
 *         type: string
 *         format: uuid
 *       description: The level ID
 *
 *     languageId:
 *       in: path
 *       name: id
 *       required: true
 *       schema:
 *         type: string
 *         format: uuid
 *       description: The language ID
 *
 *     courseId:
 *       in: path
 *       name: id
 *       required: true
 *       schema:
 *         type: string
 *         format: uuid
 *       description: The course ID
 *
 *     flashcardId:
 *       in: path
 *       name: id
 *       required: true
 *       schema:
 *         type: string
 *         format: uuid
 *       description: The flashcard ID
 *
 *     quizId:
 *       in: path
 *       name: id
 *       required: true
 *       schema:
 *         type: string
 *         format: uuid
 *       description: The quiz ID
 *
 *     questionId:
 *       in: path
 *       name: id
 *       required: true
 *       schema:
 *         type: string
 *         format: uuid
 *       description: The question ID
 *
 *     groupId:
 *       in: path
 *       name: id
 *       required: true
 *       schema:
 *         type: string
 *         format: uuid
 *       description: The group ID
 *
 *     supportId:
 *       in: path
 *       name: id
 *       required: true
 *       schema:
 *         type: string
 *         format: uuid
 *       description: The support ticket ID
 *
 *     notificationId:
 *       in: path
 *       name: id
 *       required: true
 *       schema:
 *         type: string
 *         format: uuid
 *       description: The notification ID
 *
 *   responses:
 *     UnauthorizedError:
 *       description: Access token is missing or invalid
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: integer
 *                 example: 401
 *               message:
 *                 type: string
 *                 example: Unauthorized attempt, login again!
 *
 *     NotFoundError:
 *       description: Resource not found
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: integer
 *                 example: 404
 *               message:
 *                 type: string
 *                 example: Resource not found
 *
 *     ValidationError:
 *       description: Validation error
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: integer
 *                 example: 400
 *               message:
 *                 type: string
 *                 example: Validation error
 *
 *     ServerError:
 *       description: Internal server error
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: integer
 *                 example: 500
 *               message:
 *                 type: string
 *                 example: Internal server error
 *
 * tags:
 *   - name: Auth
 *     description: Authentication endpoints
 *   - name: Users
 *     description: User management endpoints
 *   - name: Levels
 *     description: Level management endpoints
 *   - name: Languages
 *     description: Language management endpoints
 *   - name: Courses
 *     description: Course management endpoints
 *   - name: Flashcards
 *     description: Flashcard management endpoints
 *   - name: Quizzes
 *     description: Quiz management endpoints
 *   - name: Questions
 *     description: Question management endpoints
 *   - name: Groups
 *     description: Group management endpoints
 *   - name: Support
 *     description: Support ticket management endpoints
 *   - name: Notifications
 *     description: Notification management endpoints
 *
 * security:
 *   - bearerAuth: []
 *
 * paths:
 *   /users/verify:
 *     post:
 *       summary: Verify OTP
 *       tags: [Auth]
 *       requestBody:
 *         required: true
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               required:
 *                 - email
 *                 - otp
 *               properties:
 *                 email:
 *                   type: string
 *                   format: email
 *                 otp:
 *                   type: string
 *       responses:
 *         200:
 *           description: OTP verified successfully
 *         400:
 *           $ref: '#/components/responses/ValidationError'
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *
 *   /users/signup:
 *     post:
 *       summary: Register a new user
 *       tags: [Auth]
 *       requestBody:
 *         required: true
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               required:
 *                 - username
 *                 - email
 *                 - password
 *               properties:
 *                 username:
 *                   type: string
 *                 email:
 *                   type: string
 *                   format: email
 *                 password:
 *                   type: string
 *                   format: password
 *       responses:
 *         200:
 *           description: User registered successfully
 *         400:
 *           $ref: '#/components/responses/ValidationError'
 *
 *   /users/google:
 *     post:
 *       summary: Google social authentication
 *       tags: [Auth]
 *       requestBody:
 *         required: true
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               required:
 *                 - token
 *               properties:
 *                 token:
 *                   type: string
 *       responses:
 *         200:
 *           description: Authentication successful
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *
 *   /users/oauth:
 *     post:
 *       summary: OAuth authentication
 *       tags: [Auth]
 *       requestBody:
 *         required: true
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               required:
 *                 - token
 *               properties:
 *                 token:
 *                   type: string
 *       responses:
 *         200:
 *           description: Authentication successful
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *
 *   /users/login:
 *     post:
 *       summary: User login
 *       tags: [Auth]
 *       requestBody:
 *         required: true
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               required:
 *                 - email
 *                 - password
 *               properties:
 *                 email:
 *                   type: string
 *                   format: email
 *                 password:
 *                   type: string
 *                   format: password
 *       responses:
 *         200:
 *           description: Login successful
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *
 *   /users/upload:
 *     post:
 *       summary: Upload user image
 *       tags: [Users]
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               type: object
 *               required:
 *                 - image
 *               properties:
 *                 image:
 *                   type: string
 *                   format: binary
 *       responses:
 *         200:
 *           description: Image uploaded successfully
 *         400:
 *           $ref: '#/components/responses/ValidationError'
 *
 *   /users/me:
 *     get:
 *       summary: Get current user profile
 *       tags: [Users]
 *       security:
 *         - bearerAuth: []
 *       responses:
 *         200:
 *           description: User profile retrieved successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: string
 *                     example: success
 *                   data:
 *                     $ref: '#/components/schemas/User'
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *
 *   /users/profile:
 *     patch:
 *       summary: Update user profile
 *       tags: [Users]
 *       security:
 *         - bearerAuth: []
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               type: object
 *               properties:
 *                 username:
 *                   type: string
 *                 profilePic:
 *                   type: string
 *                   format: binary
 *       responses:
 *         200:
 *           description: Profile updated successfully
 *         400:
 *           $ref: '#/components/responses/ValidationError'
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *
 *   /users/fcm:
 *     patch:
 *       summary: Update FCM token
 *       tags: [Users]
 *       security:
 *         - bearerAuth: []
 *       requestBody:
 *         required: true
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               required:
 *                 - FCM
 *               properties:
 *                 FCM:
 *                   type: string
 *       responses:
 *         200:
 *           description: FCM token updated successfully
 *         400:
 *           $ref: '#/components/responses/ValidationError'
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *
 *   /levels:
 *     get:
 *       summary: Get all levels
 *       tags: [Levels]
 *       parameters:
 *         - in: query
 *           name: page
 *           schema:
 *             type: integer
 *             default: 1
 *           description: Page number for pagination
 *         - in: query
 *           name: limit
 *           schema:
 *             type: integer
 *             default: 10
 *           description: Number of items per page
 *         - in: query
 *           name: name
 *           schema:
 *             type: string
 *           description: Filter by level name
 *         - in: query
 *           name: description
 *           schema:
 *             type: string
 *           description: Filter by level description
 *       responses:
 *         200:
 *           description: List of levels
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: string
 *                     example: success
 *                   data:
 *                     type: object
 *                     properties:
 *                       rows:
 *                         type: array
 *                         items:
 *                           $ref: '#/components/schemas/Level'
 *                       count:
 *                         type: integer
 *                         description: Total number of levels
 *
 *   /levels/{id}:
 *     get:
 *       summary: Get a level by ID
 *       tags: [Levels]
 *       parameters:
 *         - $ref: '#/components/parameters/levelId'
 *       responses:
 *         200:
 *           description: Level details
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: string
 *                     example: success
 *                   data:
 *                     $ref: '#/components/schemas/Level'
 *         404:
 *           $ref: '#/components/responses/NotFoundError'
 *
 *   /languages:
 *     get:
 *       summary: Get all languages
 *       tags: [Languages]
 *       parameters:
 *         - in: query
 *           name: page
 *           schema:
 *             type: integer
 *             default: 1
 *           description: Page number for pagination
 *         - in: query
 *           name: limit
 *           schema:
 *             type: integer
 *             default: 10
 *           description: Number of items per page
 *         - in: query
 *           name: name
 *           schema:
 *             type: string
 *           description: Filter by language name
 *         - in: query
 *           name: code
 *           schema:
 *             type: string
 *           description: Filter by language code
 *       responses:
 *         200:
 *           description: List of languages
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: string
 *                     example: success
 *                   data:
 *                     type: object
 *                     properties:
 *                       rows:
 *                         type: array
 *                         items:
 *                           $ref: '#/components/schemas/Language'
 *                       count:
 *                         type: integer
 *                         description: Total number of languages
 *
 *   /languages/{id}:
 *     get:
 *       summary: Get a language by ID
 *       tags: [Languages]
 *       parameters:
 *         - $ref: '#/components/parameters/languageId'
 *       responses:
 *         200:
 *           description: Language details
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: string
 *                     example: success
 *                   data:
 *                     $ref: '#/components/schemas/Language'
 *         404:
 *           $ref: '#/components/responses/NotFoundError'
 *
 *   /courses:
 *     get:
 *       summary: Get all courses
 *       tags: [Courses]
 *       parameters:
 *         - in: query
 *           name: page
 *           schema:
 *             type: integer
 *             default: 1
 *           description: Page number for pagination
 *         - in: query
 *           name: limit
 *           schema:
 *             type: integer
 *             default: 10
 *           description: Number of items per page
 *         - in: query
 *           name: name
 *           schema:
 *             type: string
 *           description: Filter by course name
 *         - in: query
 *           name: languageId
 *           schema:
 *             type: string
 *             format: uuid
 *           description: Filter by language ID
 *         - in: query
 *           name: levelId
 *           schema:
 *             type: string
 *             format: uuid
 *           description: Filter by level ID
 *       responses:
 *         200:
 *           description: List of courses
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: string
 *                     example: success
 *                   data:
 *                     type: object
 *                     properties:
 *                       rows:
 *                         type: array
 *                         items:
 *                           $ref: '#/components/schemas/Course'
 *                       count:
 *                         type: integer
 *                         description: Total number of courses
 *
 *   /courses/{id}:
 *     get:
 *       summary: Get a course by ID
 *       tags: [Courses]
 *       parameters:
 *         - $ref: '#/components/parameters/courseId'
 *       responses:
 *         200:
 *           description: Course details
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: string
 *                     example: success
 *                   data:
 *                     $ref: '#/components/schemas/Course'
 *         404:
 *           $ref: '#/components/responses/NotFoundError'
 *
 *   /flashcards:
 *     get:
 *       summary: Get all flashcards
 *       tags: [Flashcards]
 *       parameters:
 *         - in: query
 *           name: page
 *           schema:
 *             type: integer
 *             default: 1
 *           description: Page number for pagination
 *         - in: query
 *           name: limit
 *           schema:
 *             type: integer
 *             default: 10
 *           description: Number of items per page
 *         - in: query
 *           name: word
 *           schema:
 *             type: string
 *           description: Filter by word
 *         - in: query
 *           name: languageId
 *           schema:
 *             type: string
 *             format: uuid
 *           description: Filter by language ID
 *       responses:
 *         200:
 *           description: List of flashcards
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: string
 *                     example: success
 *                   data:
 *                     type: object
 *                     properties:
 *                       rows:
 *                         type: array
 *                         items:
 *                           $ref: '#/components/schemas/Flashcard'
 *                       count:
 *                         type: integer
 *                         description: Total number of flashcards
 *
 *   /flashcards/{id}:
 *     get:
 *       summary: Get a flashcard by ID
 *       tags: [Flashcards]
 *       parameters:
 *         - $ref: '#/components/parameters/flashcardId'
 *       responses:
 *         200:
 *           description: Flashcard details
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: string
 *                     example: success
 *                   data:
 *                     $ref: '#/components/schemas/Flashcard'
 *         404:
 *           $ref: '#/components/responses/NotFoundError'
 *
 *   /flashcards/likedislike/{id}:
 *     get:
 *       summary: Like or dislike a flashcard
 *       tags: [Flashcards]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/flashcardId'
 *         - in: query
 *           name: type
 *           schema:
 *             type: string
 *             enum: [like, dislike]
 *           description: Type of action (like or dislike)
 *       responses:
 *         200:
 *           description: Action successful
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         404:
 *           $ref: '#/components/responses/NotFoundError'
 *
 *   /flashcards/bookmark/{id}:
 *     get:
 *       summary: Toggle bookmark for a flashcard
 *       tags: [Flashcards]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/flashcardId'
 *       responses:
 *         200:
 *           description: Bookmark toggled successfully
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         404:
 *           $ref: '#/components/responses/NotFoundError'
 *
 *   /quizzes:
 *     get:
 *       summary: Get all quizzes
 *       tags: [Quizzes]
 *       parameters:
 *         - in: query
 *           name: page
 *           schema:
 *             type: integer
 *             default: 1
 *           description: Page number for pagination
 *         - in: query
 *           name: limit
 *           schema:
 *             type: integer
 *             default: 10
 *           description: Number of items per page
 *         - in: query
 *           name: name
 *           schema:
 *             type: string
 *           description: Filter by quiz name
 *       responses:
 *         200:
 *           description: List of quizzes
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: string
 *                     example: success
 *                   data:
 *                     type: object
 *                     properties:
 *                       rows:
 *                         type: array
 *                         items:
 *                           $ref: '#/components/schemas/Quiz'
 *                       count:
 *                         type: integer
 *                         description: Total number of quizzes
 *
 *   /quizzes/{id}:
 *     get:
 *       summary: Get a quiz by ID
 *       tags: [Quizzes]
 *       parameters:
 *         - $ref: '#/components/parameters/quizId'
 *       responses:
 *         200:
 *           description: Quiz details
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: string
 *                     example: success
 *                   data:
 *                     $ref: '#/components/schemas/Quiz'
 *         404:
 *           $ref: '#/components/responses/NotFoundError'
 *
 *   /questions:
 *     get:
 *       summary: Get all questions
 *       tags: [Questions]
 *       parameters:
 *         - in: query
 *           name: page
 *           schema:
 *             type: integer
 *             default: 1
 *           description: Page number for pagination
 *         - in: query
 *           name: limit
 *           schema:
 *             type: integer
 *             default: 10
 *           description: Number of items per page
 *         - in: query
 *           name: quizId
 *           schema:
 *             type: string
 *             format: uuid
 *           description: Filter by quiz ID
 *       responses:
 *         200:
 *           description: List of questions
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: string
 *                     example: success
 *                   data:
 *                     type: object
 *                     properties:
 *                       rows:
 *                         type: array
 *                         items:
 *                           $ref: '#/components/schemas/Question'
 *                       count:
 *                         type: integer
 *                         description: Total number of questions
 *
 *   /questions/{id}:
 *     get:
 *       summary: Get a question by ID
 *       tags: [Questions]
 *       parameters:
 *         - $ref: '#/components/parameters/questionId'
 *       responses:
 *         200:
 *           description: Question details
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: string
 *                     example: success
 *                   data:
 *                     $ref: '#/components/schemas/Question'
 *         404:
 *           $ref: '#/components/responses/NotFoundError'
 *
 *   /groups:
 *     get:
 *       summary: Get all groups
 *       tags: [Groups]
 *       parameters:
 *         - in: query
 *           name: page
 *           schema:
 *             type: integer
 *             default: 1
 *           description: Page number for pagination
 *         - in: query
 *           name: limit
 *           schema:
 *             type: integer
 *             default: 10
 *           description: Number of items per page
 *         - in: query
 *           name: name
 *           schema:
 *             type: string
 *           description: Filter by group name
 *         - in: query
 *           name: languageId
 *           schema:
 *             type: string
 *             format: uuid
 *           description: Filter by language ID
 *       responses:
 *         200:
 *           description: List of groups
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: string
 *                     example: success
 *                   data:
 *                     type: object
 *                     properties:
 *                       rows:
 *                         type: array
 *                         items:
 *                           $ref: '#/components/schemas/Group'
 *                       count:
 *                         type: integer
 *                         description: Total number of groups
 *
 *   /groups/{id}:
 *     get:
 *       summary: Get a group by ID
 *       tags: [Groups]
 *       parameters:
 *         - $ref: '#/components/parameters/groupId'
 *       responses:
 *         200:
 *           description: Group details
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: string
 *                     example: success
 *                   data:
 *                     $ref: '#/components/schemas/Group'
 *         404:
 *           $ref: '#/components/responses/NotFoundError'
 *
 *   /support:
 *     get:
 *       summary: Get all support tickets
 *       tags: [Support]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - in: query
 *           name: page
 *           schema:
 *             type: integer
 *             default: 1
 *           description: Page number for pagination
 *         - in: query
 *           name: limit
 *           schema:
 *             type: integer
 *             default: 10
 *           description: Number of items per page
 *         - in: query
 *           name: status
 *           schema:
 *             type: string
 *             enum: [open, in-progress, closed]
 *           description: Filter by ticket status
 *       responses:
 *         200:
 *           description: List of support tickets
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: string
 *                     example: success
 *                   data:
 *                     type: object
 *                     properties:
 *                       rows:
 *                         type: array
 *                         items:
 *                           $ref: '#/components/schemas/Support'
 *                       count:
 *                         type: integer
 *                         description: Total number of support tickets
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *
 *   /support/{id}:
 *     get:
 *       summary: Get a support ticket by ID
 *       tags: [Support]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/supportId'
 *       responses:
 *         200:
 *           description: Support ticket details
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: string
 *                     example: success
 *                   data:
 *                     $ref: '#/components/schemas/Support'
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         404:
 *           $ref: '#/components/responses/NotFoundError'
 *
 *   /notifications:
 *     get:
 *       summary: Get all notifications
 *       tags: [Notifications]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - in: query
 *           name: page
 *           schema:
 *             type: integer
 *             default: 1
 *           description: Page number for pagination
 *         - in: query
 *           name: limit
 *           schema:
 *             type: integer
 *             default: 10
 *           description: Number of items per page
 *       responses:
 *         200:
 *           description: List of notifications
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: string
 *                     example: success
 *                   data:
 *                     type: object
 *                     properties:
 *                       rows:
 *                         type: array
 *                         items:
 *                           $ref: '#/components/schemas/Notification'
 *                       count:
 *                         type: integer
 *                         description: Total number of notifications
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *
 *   /notifications/{id}:
 *     get:
 *       summary: Get a notification by ID
 *       tags: [Notifications]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/notificationId'
 *       responses:
 *         200:
 *           description: Notification details
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: string
 *                     example: success
 *                   data:
 *                     $ref: '#/components/schemas/Notification'
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         404:
 *           $ref: '#/components/responses/NotFoundError'
 *
 *   /notifications/click:
 *     post:
 *       summary: Record notification click
 *       tags: [Notifications]
 *       security:
 *         - bearerAuth: []
 *       requestBody:
 *         required: true
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               required:
 *                 - notificationId
 *               properties:
 *                 notificationId:
 *                   type: string
 *                   format: uuid
 *       responses:
 *         200:
 *           description: Click recorded successfully
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         404:
 *           $ref: '#/components/responses/NotFoundError'
 */
