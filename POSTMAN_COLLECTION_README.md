# Oroi Backend API - Postman Collection

This repository contains a comprehensive Postman collection for the Oroi language learning platform backend API.

## Files

- `oroi_complete_postman_collection.json` - Complete Postman collection with all API endpoints
- `postman_collection.json` - Original collection (legacy)

## Collection Overview

The collection includes all API endpoints organized into the following categories:

### 🔐 Authentication
- User Signup
- OTP Verification
- User Login
- Google OAuth
- Google Social Auth

### 👤 User Profile
- Get User Profile
- Update User Profile
- Update FCM Token
- Upload Image

### 🏛️ Admin Authentication
- Admin Login
- Create Admin User

### 📊 Admin Dashboard
- Get Analytics
- Get Daily Data
- Get Language Specific Data
- Get Top Like/Dislike Data

### 👥 Admin User Management
- Get All Users
- Get User by ID
- Block/Unblock User
- Delete User

### 🌍 Languages
- Get All Languages
- Get Language by ID

### 📚 Flashcards
- Get All Flashcards
- Get Flashcard by ID
- Get Flashcard Like/Dislike Status
- Get Flashcard Bookmark Status

### 💝 Flashcard Interactions
- Flashcard Bookmarks (GET)
- Flashcard Likes (GET)
- Flashcard Dislikes (GET)

### 🎓 Courses
- Get All Courses
- Get Course by ID
- Course Flashcards
- Course Quizzes

### 👥 Groups
- Get All Groups
- Get Group by ID
- Group Flashcards

### 🧠 Quizzes & Levels
- Get All Quizzes
- Get Quiz by ID
- Get All Levels
- Get Level by ID

### 🔔 Notifications
- Get All Notifications
- Get Notification by ID
- Click Notification

### 🔗 User Relations
- User Flashcards
- User Groups
- User Courses
- User Languages

### ⚙️ Admin Content Management
- Create Language
- Create Flashcard
- Manage App Configuration

## Setup Instructions

### 1. Import the Collection

1. Open Postman
2. Click "Import" button
3. Select `oroi_complete_postman_collection.json`
4. The collection will be imported with all endpoints and variables

### 2. Configure Environment Variables

The collection includes the following variables that you can customize:

| Variable | Default Value | Description |
|----------|---------------|-------------|
| `base_url` | `http://localhost:3000` | API base URL |
| `token` | `` | User authentication token |
| `admin_token` | `` | Admin authentication token |
| `user_id` | `` | User ID for testing |
| `language_id` | `` | Language ID for testing |
| `flashcard_id` | `` | Flashcard ID for testing |
| `course_id` | `` | Course ID for testing |
| `group_id` | `` | Group ID for testing |
| `quiz_id` | `` | Quiz ID for testing |
| `level_id` | `` | Level ID for testing |
| `notification_id` | `` | Notification ID for testing |

### 3. Authentication Flow

#### For Regular Users:
1. **Sign Up**: Use "User Signup" endpoint
2. **Verify OTP**: Use "Verify OTP" endpoint with the OTP received
3. **Login**: Use "User Login" endpoint
   - The login request automatically saves the token to `{{token}}` variable
4. **Use Protected Endpoints**: All user endpoints will use the saved token

#### For Admin Users:
1. **Admin Login**: Use "Admin Login" endpoint
   - The login request automatically saves the token to `{{admin_token}}` variable
2. **Use Admin Endpoints**: All admin endpoints will use the saved admin token

### 4. Testing Different Environments

To test against different environments:

1. **Development**: Set `base_url` to `http://localhost:3000`
2. **Production**: Set `base_url` to `https://api.oroi.com`

## API Authentication

The API uses JWT Bearer token authentication:

- **Header**: `Authorization: Bearer <token>`
- **User Token**: Obtained from login endpoints, stored in `{{token}}`
- **Admin Token**: Obtained from admin login, stored in `{{admin_token}}`

## Request Examples

### User Registration Flow
```
1. POST /api/v1/user/signup
2. POST /api/v1/user/verify (with OTP)
3. POST /api/v1/user/login
```

### Admin Content Creation
```
1. POST /api/v1/admin/user/login
2. POST /api/v1/admin/language (create language)
3. POST /api/v1/admin/flashcard (create flashcard)
```

## Error Handling

The API returns standard HTTP status codes:
- `200` - Success
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `500` - Internal Server Error

## Rate Limiting

Some endpoints have rate limiting applied. Check the API documentation for specific limits.

## Support

For API documentation and support:
- Swagger UI: `http://localhost:3000/api-docs` (development)
- Contact: <EMAIL>

## Notes

- All POST/PATCH requests require appropriate `Content-Type` headers
- File uploads use `multipart/form-data`
- JSON requests use `application/json`
- Some admin endpoints require specific roles (Admin/SuperAdmin)
- The collection includes automatic token management for login endpoints
