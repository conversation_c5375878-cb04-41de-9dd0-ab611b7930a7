"use strict";

const router = require("express").Router();
const courseQuiz = require("./controller");
const joiSchema = require("./joiSchema");
const { joiValidator } = require("../../middlewares/joiValidator");
// const { authMiddleware, protectRoute } = require("../../middlewares/auth");


router
  .route("/")
  .get(courseQuiz.getAll)
  .post(joiValidator(joiSchema.create), courseQuiz.add);
router
  .route("/:id")
  .get(courseQuiz.getById)
  .patch(joiValidator(joiSchema.update), courseQuiz.update)
  .delete(courseQuiz.delete);

module.exports = router;
