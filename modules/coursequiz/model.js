"use strict";
const { DataTypes } = require("sequelize");
const { v4: uuidv4 } = require("uuid");
const sequelize = require("../../config/db");
const Quiz = require("../quiz/model");
const Course = require("../course/model");

const CourseQuiz = sequelize.define(
  "courseQuiz",
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: uuidv4,
      allowNull: false,
      primaryKey: true,
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
  },
  {
    paranoid: true,
  }
);

// Relationships
Course.hasMany(CourseQuiz, { foreignKey: { allowNull: false } });
CourseQuiz.belongsTo(Course);

Quiz.hasMany(CourseQuiz, { foreignKey: { allowNull: false } });
CourseQuiz.belongsTo(Quiz);

module.exports = CourseQuiz;
