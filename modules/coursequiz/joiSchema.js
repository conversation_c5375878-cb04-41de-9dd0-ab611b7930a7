const Joi = require("joi");

module.exports = {
  create: Joi.object().keys({
    isActive: Joi.boolean(),
    courseId: Joi.string().guid({ version: "uuidv4" }).required(),
    quizId: Joi.string().guid({ version: "uuidv4" }).required(),
  }),
  update: Joi.object().keys({
    isActive: Joi.boolean(),
    courseId: Joi.string().guid({ version: "uuidv4" }),
    quizId: Joi.string().guid({ version: "uuidv4" }),
  }),
};
