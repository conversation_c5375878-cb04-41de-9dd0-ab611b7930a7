/**
 * @swagger
 * components:
 *   schemas:
 *     coursequiz:
 *       type: object
 *       required:
 *         
 *       properties:
 *         id:
           type: string
           description: The id of the coursequiz
         defaultValue:
           type: string
           description: The defaultValue of the coursequiz
         allowNull:
           type: string
           description: The allowNull of the coursequiz
         primaryKey:
           type: string
           description: The primaryKey of the coursequiz
         :
           type: string
           description: The  of the coursequiz
 *       example:
 *         id: "Example id"
         defaultValue: "Example defaultValue"
         allowNull: "Example allowNull"
         primaryKey: "Example primaryKey"
         : "Example "
 *
 *     CreatecoursequizRequest:
 *       type: object
 *       required:
 *         
 *       properties:
 *         isActive:
           type: string
         courseId:
           type: string
 *       example:
 *         isActive: "Example isActive"
         courseId: "Example courseId"
 *
 *     UpdatecoursequizRequest:
 *       type: object
 *       properties:
 *         isActive:
           type: string
         courseId:
           type: string
 *       example:
 *         isActive: "Example isActive"
         courseId: "Example courseId"
 *
 *   parameters:
 *     coursequizId:
 *       in: path
 *       name: id
 *       required: true
 *       schema:
 *         type: string
 *         format: uuid
 *       description: The coursequiz ID
 *
 *   responses:
 *     UnauthorizedError:
 *       description: Access token is missing or invalid
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: integer
 *                 example: 401
 *               message:
 *                 type: string
 *                 example: Unauthorized attempt, login again!
 *
 * paths:
 *   /api/v1/coursequiz:
 *     get:
 *       summary: Get all coursequizs
 *       tags: [coursequizs]
 *       parameters:
 *         - in: query
 *           name: page
 *           schema:
 *             type: integer
 *             default: 1
 *           description: Page number for pagination
 *         - in: query
 *           name: limit
 *           schema:
 *             type: integer
 *             default: 10
 *           description: Number of items per page
 *       responses:
 *         200:
 *           description: List of coursequizs
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: coursequizs retrieved successfully
 *                   data:
 *                     type: object
 *                     properties:
 *                       coursequizs:
 *                         type: array
 *                         items:
 *                           $ref: '#/components/schemas/coursequiz'
 *                       pagination:
 *                         type: object
 *                         properties:
 *                           total:
 *                             type: integer
 *                             description: Total number of coursequizs
 *                           page:
 *                             type: integer
 *                             description: Current page number
 *                           limit:
 *                             type: integer
 *                             description: Number of items per page
 *                           pages:
 *                             type: integer
 *                             description: Total number of pages
 *
 *   /api/v1/coursequiz/{id}:
 *     get:
 *       summary: Get coursequiz by ID
 *       tags: [coursequizs]
 *       parameters:
 *         - $ref: '#/components/parameters/coursequizId'
 *       responses:
 *         200:
 *           description: coursequiz retrieved successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: coursequiz retrieved successfully
 *                   data:
 *                     $ref: '#/components/schemas/coursequiz'
 *         404:
 *           description: coursequiz not found
 *
 *   /api/v1/admin/coursequiz:
 *     post:
 *       summary: Create a new coursequiz
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/CreatecoursequizRequest'
 *       responses:
 *         201:
 *           description: coursequiz created successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 201
 *                   message:
 *                     type: string
 *                     example: coursequiz created successfully
 *                   data:
 *                     $ref: '#/components/schemas/coursequiz'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *
 *   /api/v1/admin/coursequiz/{id}:
 *     patch:
 *       summary: Update a coursequiz
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/coursequizId'
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/UpdatecoursequizRequest'
 *       responses:
 *         200:
 *           description: coursequiz updated successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: coursequiz updated successfully
 *                   data:
 *                     $ref: '#/components/schemas/coursequiz'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: coursequiz not found
 *
 *     delete:
 *       summary: Delete a coursequiz
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/coursequizId'
 *       responses:
 *         200:
 *           description: coursequiz deleted successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: coursequiz deleted successfully
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: coursequiz not found
 */