/**
 * @swagger
 * components:
 *   schemas:
 *     support:
 *       type: object
 *       required:
 *         - .required()
         - .required()
 *       properties:
 *         id:
           type: string
           description: The id of the support
         defaultValue:
           type: string
           description: The defaultValue of the support
         allowNull:
           type: string
           description: The allowNull of the support
         primaryKey:
           type: string
           description: The primaryKey of the support
         :
           type: string
           description: The  of the support
 *       example:
 *         id: "Example id"
         defaultValue: "Example defaultValue"
         allowNull: "Example allowNull"
         primaryKey: "Example primaryKey"
         : "Example "
 *
 *     CreatesupportRequest:
 *       type: object
 *       required:
 *         - .required()
         - .required()
 *       properties:
 *         name:
           type: string
         description:
           type: string
         :
           type: string
 *       example:
 *         name: "Example name"
         description: "Example description"
         : "Example "
 *
 *     UpdatesupportRequest:
 *       type: object
 *       properties:
 *         name:
           type: string
         description:
           type: string
         :
           type: string
 *       example:
 *         name: "Example name"
         description: "Example description"
         : "Example "
 *
 *   parameters:
 *     supportId:
 *       in: path
 *       name: id
 *       required: true
 *       schema:
 *         type: string
 *         format: uuid
 *       description: The support ID
 *
 *   responses:
 *     UnauthorizedError:
 *       description: Access token is missing or invalid
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: integer
 *                 example: 401
 *               message:
 *                 type: string
 *                 example: Unauthorized attempt, login again!
 *
 * paths:
 *   /api/v1/support:
 *     get:
 *       summary: Get all supports
 *       tags: [supports]
 *       parameters:
 *         - in: query
 *           name: page
 *           schema:
 *             type: integer
 *             default: 1
 *           description: Page number for pagination
 *         - in: query
 *           name: limit
 *           schema:
 *             type: integer
 *             default: 10
 *           description: Number of items per page
 *       responses:
 *         200:
 *           description: List of supports
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: supports retrieved successfully
 *                   data:
 *                     type: object
 *                     properties:
 *                       supports:
 *                         type: array
 *                         items:
 *                           $ref: '#/components/schemas/support'
 *                       pagination:
 *                         type: object
 *                         properties:
 *                           total:
 *                             type: integer
 *                             description: Total number of supports
 *                           page:
 *                             type: integer
 *                             description: Current page number
 *                           limit:
 *                             type: integer
 *                             description: Number of items per page
 *                           pages:
 *                             type: integer
 *                             description: Total number of pages
 *
 *   /api/v1/support/{id}:
 *     get:
 *       summary: Get support by ID
 *       tags: [supports]
 *       parameters:
 *         - $ref: '#/components/parameters/supportId'
 *       responses:
 *         200:
 *           description: support retrieved successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: support retrieved successfully
 *                   data:
 *                     $ref: '#/components/schemas/support'
 *         404:
 *           description: support not found
 *
 *   /api/v1/admin/support:
 *     post:
 *       summary: Create a new support
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/CreatesupportRequest'
 *       responses:
 *         201:
 *           description: support created successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 201
 *                   message:
 *                     type: string
 *                     example: support created successfully
 *                   data:
 *                     $ref: '#/components/schemas/support'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *
 *   /api/v1/admin/support/{id}:
 *     patch:
 *       summary: Update a support
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/supportId'
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/UpdatesupportRequest'
 *       responses:
 *         200:
 *           description: support updated successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: support updated successfully
 *                   data:
 *                     $ref: '#/components/schemas/support'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: support not found
 *
 *     delete:
 *       summary: Delete a support
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/supportId'
 *       responses:
 *         200:
 *           description: support deleted successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: support deleted successfully
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: support not found
 */