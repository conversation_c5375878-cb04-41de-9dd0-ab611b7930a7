"use strict";

const router = require("express").Router();
const support = require("./controller");
const joiSchema = require("./joiSchema");
const { joiValidator } = require("../../middlewares/joiValidator");
// const { authMiddleware, protectRoute } = require("../../middlewares/auth");


router
  .route("/")
  .get(support.getAll)
  .post(joiValidator(joiSchema.create), support.add);
router
  .route("/:id")
  .get(support.getById)
  .patch(joiValidator(joiSchema.update), support.update)
  .delete(support.delete);

module.exports = router;
