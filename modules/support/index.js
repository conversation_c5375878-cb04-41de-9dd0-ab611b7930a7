"use strict";

const router = require("express").Router();
const support = require("./controller");
// const { authMiddleware, protectRoute } = require("../../middlewares/auth");
// const { joiValidator } = require("../../middlewares/joiValidator");

router
  .route("/")
  .get(support.getAll)

//   .post(joiValidator(joiSchema.create), support.add);
router
  .route("/:id")
  .get(support.getById)
//   .patch(joiValidator(joiSchema.update), support.update)
//   .delete(support.delete);

module.exports = router;
