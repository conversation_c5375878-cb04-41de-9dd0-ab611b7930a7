"use strict";
const { DataTypes } = require("sequelize");
const { v4: uuidv4 } = require("uuid");
const sequelize = require("../../config/db");
const User = require("../user/model");

const Support = sequelize.define(
  "support",
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: uuidv4,
      allowNull: false,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    email: {
      type: DataTypes.STRING,
      validate: {
        isEmail: true,
      },
    },
    subject: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    status: {
      type: DataTypes.STRING,
      defaultValue: 'open',
    },
    priority: {
      type: DataTypes.STRING,
      defaultValue: 'medium',
    },
    resolution: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
  },
  {
    paranoid: true,
  }
);

User.hasMany(Support, { foreignKey: 'userId' });
Support.belongsTo(User, { as: 'Creator', foreignKey: 'userId' });

User.hasMany(Support, { as: 'AssignedTickets', foreignKey: 'assignedTo' });
Support.belongsTo(User, { as: 'AssignedTo', foreignKey: 'assignedTo' });

module.exports = Support;
