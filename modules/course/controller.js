"use strict";
const sequelize = require("sequelize");
const CourseService = require("./service");
const { sqquery } = require("../../utils/query");
const Language = require("../language/model");
const Level = require("../level/model");
const { getFilePath } = require("../../middlewares/multer/helper");

exports.getAll = async (req, res, next) => {
  try {
    const data = await CourseService.findAndCountAll({
      ...sqquery(req.query, {}, ["name", "description"]),
      attributes: [
        "id",
        "name",
        "description",
        "image",
        "isActive",
        "isPremium",
        "languageId",
        "levelId",
        [
          sequelize.literal(
            `(SELECT COUNT(*) FROM "${process.env.DEVSCHEMA}"."courseFlashcards" WHERE "courseFlashcards"."courseId" = "course"."id")`
          ),
          "flashcardcounts",
        ],
      ],
      include: [
        {
          model: Language,
          required: true,
          attributes: ["code", "name", "englishName"],
        },
        {
          model: Level,
          required: true,
          attributes: ["name"],
        },
      ],
    });

    res.status(200).send({
      status: "success",
      data,
    });
  } catch (error) {
    next(error);
  }
};

exports.add = async (req, res, next) => {
  try {
    const groupImagePath = req?.files?.image
      ? getFilePath(req.files.image[0])
      : null;

    if (groupImagePath) {
      req.body.image = groupImagePath;
    }
    const data = await CourseService.create(req.body);

    res.status(200).json({
      status: "success",
      data,
    });
  } catch (error) {
    // console.error(error);
    next(error);
  }
};

exports.getById = async (req, res, next) => {
  try {
    const data = await CourseService.findOne({
      where: {
        id: req.params.id,
      },
    });

    res.status(200).send({
      status: "success",
      data,
    });
  } catch (error) {
    next(error);
  }
};

exports.update = async (req, res, next) => {
  try {
    const groupImagePath = req?.files?.image
      ? getFilePath(req.files.image[0])
      : null;

    if (groupImagePath) {
      req.body.image = groupImagePath;
    }
    const [affectedRows] = await CourseService.update(req.body, {
      where: {
        id: req.params.id,
      },
    });

    res.status(200).json({
      status: "success",
      data: {
        affectedRows,
      },
    });
  } catch (error) {
    next(error);
  }
};

exports.delete = async (req, res, next) => {
  try {
    const affectedRows = await CourseService.delete({
      where: {
        id: req.params.id,
      },
    });

    res.status(200).send({
      status: "success",
      data: {
        affectedRows,
      },
    });
  } catch (error) {
    next(error);
  }
};
