"use strict";

const router = require("express").Router();
const course = require("./controller");
// const { authMiddleware, protectRoute } = require("../../middlewares/auth");
// const { joiValidator } = require("../../middlewares/joiValidator");

router
  .route("/")
  .get(course.getAll)

//   .post(joiValidator(joiSchema.create), course.add);
router
  .route("/:id")
  .get(course.getById)
//   .patch(joiValidator(joiSchema.update), course.update)
//   .delete(course.delete);

module.exports = router;
