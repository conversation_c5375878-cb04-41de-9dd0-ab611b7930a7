"use strict";

const router = require("express").Router();
const course = require("./controller");
const joiSchema = require("./joiSchema");
const { joiValidator } = require("../../middlewares/joiValidator");
const { setUploadFolder } = require("../../middlewares/multer/helper");
const { upload } = require("../../middlewares/multer");
// const { authMiddleware, protectRoute } = require("../../middlewares/auth");

router
  .route("/")
  .get(course.getAll)
  .post(
    setUploadFolder("group"),
    upload.fields([{ name: "image", maxCount: 1 }]),
    joiValidator(joiSchema.create),
    course.add
  );
router
  .route("/:id")
  .get(course.getById)
  .patch(
    setUploadFolder("group"),
    upload.fields([{ name: "image", maxCount: 1 }]),
    joiValidator(joiSchema.update),
    course.update
  )
  .delete(course.delete);

module.exports = router;
