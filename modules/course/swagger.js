/**
 * @swagger
 * components:
 *   schemas:
 *     course:
 *       type: object
 *       required:
 *         - .required()
         - .required()
 *       properties:
 *         id:
           type: string
           description: The id of the course
         defaultValue:
           type: string
           description: The defaultValue of the course
         allowNull:
           type: string
           description: The allowNull of the course
         primaryKey:
           type: string
           description: The primaryKey of the course
         :
           type: string
           description: The  of the course
 *       example:
 *         id: "Example id"
         defaultValue: "Example defaultValue"
         allowNull: "Example allowNull"
         primaryKey: "Example primaryKey"
         : "Example "
 *
 *     CreatecourseRequest:
 *       type: object
 *       required:
 *         - .required()
         - .required()
 *       properties:
 *         name:
           type: string
         description:
           type: string
         isActive:
           type: string
         isPremium:
           type: string
         languageId:
           type: string
 *       example:
 *         name: "Example name"
         description: "Example description"
         isActive: "Example isActive"
         isPremium: "Example isPremium"
         languageId: "Example languageId"
 *
 *     UpdatecourseRequest:
 *       type: object
 *       properties:
 *         name:
           type: string
         description:
           type: string
         isActive:
           type: string
         isPremium:
           type: string
         languageId:
           type: string
 *       example:
 *         name: "Example name"
         description: "Example description"
         isActive: "Example isActive"
         isPremium: "Example isPremium"
         languageId: "Example languageId"
 *
 *   parameters:
 *     courseId:
 *       in: path
 *       name: id
 *       required: true
 *       schema:
 *         type: string
 *         format: uuid
 *       description: The course ID
 *
 *   responses:
 *     UnauthorizedError:
 *       description: Access token is missing or invalid
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: integer
 *                 example: 401
 *               message:
 *                 type: string
 *                 example: Unauthorized attempt, login again!
 *
 * paths:
 *   /api/v1/course:
 *     get:
 *       summary: Get all courses
 *       tags: [courses]
 *       parameters:
 *         - in: query
 *           name: page
 *           schema:
 *             type: integer
 *             default: 1
 *           description: Page number for pagination
 *         - in: query
 *           name: limit
 *           schema:
 *             type: integer
 *             default: 10
 *           description: Number of items per page
 *       responses:
 *         200:
 *           description: List of courses
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: courses retrieved successfully
 *                   data:
 *                     type: object
 *                     properties:
 *                       courses:
 *                         type: array
 *                         items:
 *                           $ref: '#/components/schemas/course'
 *                       pagination:
 *                         type: object
 *                         properties:
 *                           total:
 *                             type: integer
 *                             description: Total number of courses
 *                           page:
 *                             type: integer
 *                             description: Current page number
 *                           limit:
 *                             type: integer
 *                             description: Number of items per page
 *                           pages:
 *                             type: integer
 *                             description: Total number of pages
 *
 *   /api/v1/course/{id}:
 *     get:
 *       summary: Get course by ID
 *       tags: [courses]
 *       parameters:
 *         - $ref: '#/components/parameters/courseId'
 *       responses:
 *         200:
 *           description: course retrieved successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: course retrieved successfully
 *                   data:
 *                     $ref: '#/components/schemas/course'
 *         404:
 *           description: course not found
 *
 *   /api/v1/admin/course:
 *     post:
 *       summary: Create a new course
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/CreatecourseRequest'
 *       responses:
 *         201:
 *           description: course created successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 201
 *                   message:
 *                     type: string
 *                     example: course created successfully
 *                   data:
 *                     $ref: '#/components/schemas/course'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *
 *   /api/v1/admin/course/{id}:
 *     patch:
 *       summary: Update a course
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/courseId'
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/UpdatecourseRequest'
 *       responses:
 *         200:
 *           description: course updated successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: course updated successfully
 *                   data:
 *                     $ref: '#/components/schemas/course'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: course not found
 *
 *     delete:
 *       summary: Delete a course
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/courseId'
 *       responses:
 *         200:
 *           description: course deleted successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: course deleted successfully
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: course not found
 */