"use strict";

const router = require("express").Router();
const groupReport = require("./controller");
const joiSchema = require("./joiSchema");
const { joiValidator } = require("../../middlewares/joiValidator");
// const { authMiddleware, protectRoute } = require("../../middlewares/auth");


router
  .route("/")
  .get(groupReport.getAll)
  .post(joiValidator(joiSchema.create), groupReport.add);
router
  .route("/:id")
  .get(groupReport.getById)
  .patch(joiValidator(joiSchema.update), groupReport.update)
  .delete(groupReport.delete);

module.exports = router;
