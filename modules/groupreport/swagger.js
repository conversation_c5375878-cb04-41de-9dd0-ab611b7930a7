/**
 * @swagger
 * components:
 *   schemas:
 *     groupreport:
 *       type: object
 *       required:
 *         
 *       properties:
 *         id:
           type: string
           description: The id of the groupreport
         defaultValue:
           type: string
           description: The defaultValue of the groupreport
         allowNull:
           type: string
           description: The allowNull of the groupreport
         primaryKey:
           type: string
           description: The primaryKey of the groupreport
         :
           type: string
           description: The  of the groupreport
 *       example:
 *         id: "Example id"
         defaultValue: "Example defaultValue"
         allowNull: "Example allowNull"
         primaryKey: "Example primaryKey"
         : "Example "
 *
 *     CreategroupreportRequest:
 *       type: object
 *       required:
 *         
 *       properties:
 *         // userId:
           type: string
 *       example:
 *         // userId: "Example // userId"
 *
 *     UpdategroupreportRequest:
 *       type: object
 *       properties:
 *         // userId:
           type: string
 *       example:
 *         // userId: "Example // userId"
 *
 *   parameters:
 *     groupreportId:
 *       in: path
 *       name: id
 *       required: true
 *       schema:
 *         type: string
 *         format: uuid
 *       description: The groupreport ID
 *
 *   responses:
 *     UnauthorizedError:
 *       description: Access token is missing or invalid
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: integer
 *                 example: 401
 *               message:
 *                 type: string
 *                 example: Unauthorized attempt, login again!
 *
 * paths:
 *   /api/v1/groupreport:
 *     get:
 *       summary: Get all groupreports
 *       tags: [groupreports]
 *       parameters:
 *         - in: query
 *           name: page
 *           schema:
 *             type: integer
 *             default: 1
 *           description: Page number for pagination
 *         - in: query
 *           name: limit
 *           schema:
 *             type: integer
 *             default: 10
 *           description: Number of items per page
 *       responses:
 *         200:
 *           description: List of groupreports
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: groupreports retrieved successfully
 *                   data:
 *                     type: object
 *                     properties:
 *                       groupreports:
 *                         type: array
 *                         items:
 *                           $ref: '#/components/schemas/groupreport'
 *                       pagination:
 *                         type: object
 *                         properties:
 *                           total:
 *                             type: integer
 *                             description: Total number of groupreports
 *                           page:
 *                             type: integer
 *                             description: Current page number
 *                           limit:
 *                             type: integer
 *                             description: Number of items per page
 *                           pages:
 *                             type: integer
 *                             description: Total number of pages
 *
 *   /api/v1/groupreport/{id}:
 *     get:
 *       summary: Get groupreport by ID
 *       tags: [groupreports]
 *       parameters:
 *         - $ref: '#/components/parameters/groupreportId'
 *       responses:
 *         200:
 *           description: groupreport retrieved successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: groupreport retrieved successfully
 *                   data:
 *                     $ref: '#/components/schemas/groupreport'
 *         404:
 *           description: groupreport not found
 *
 *   /api/v1/admin/groupreport:
 *     post:
 *       summary: Create a new groupreport
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/CreategroupreportRequest'
 *       responses:
 *         201:
 *           description: groupreport created successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 201
 *                   message:
 *                     type: string
 *                     example: groupreport created successfully
 *                   data:
 *                     $ref: '#/components/schemas/groupreport'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *
 *   /api/v1/admin/groupreport/{id}:
 *     patch:
 *       summary: Update a groupreport
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/groupreportId'
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/UpdategroupreportRequest'
 *       responses:
 *         200:
 *           description: groupreport updated successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: groupreport updated successfully
 *                   data:
 *                     $ref: '#/components/schemas/groupreport'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: groupreport not found
 *
 *     delete:
 *       summary: Delete a groupreport
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/groupreportId'
 *       responses:
 *         200:
 *           description: groupreport deleted successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: groupreport deleted successfully
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: groupreport not found
 */