"use strict";
const { DataTypes } = require("sequelize");
const { v4: uuidv4 } = require("uuid");
const sequelize = require("../../config/db");
const User = require("../user/model");
const Group = require("../group/model");

const GroupReport = sequelize.define("groupReport", {
  id: {
    type: DataTypes.UUID,
    defaultValue: uuidv4,
    allowNull: false,
    primaryKey: true,
  },
  title: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  reason: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  status: {
    type: DataTypes.STRING,
    defaultValue: "Pending",
  },
}, {
  paranoid: true,
});

// Relationships
User.hasMany(GroupReport, { foreignKey: { allowNull: false } });
GroupReport.belongsTo(User);

Group.hasMany(GroupReport, { foreignKey: { allowNull: false } });
GroupReport.belongsTo(Group);

module.exports = GroupReport;
