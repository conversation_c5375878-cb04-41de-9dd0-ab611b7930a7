const Joi = require("joi");

module.exports = {
  create: Joi.object().keys({
    // userId: Joi.string().guid({ version: "uuidv4" }).required(),
    title: Joi.string().required(),
    reason: Joi.string().required(),
    // status: Joi.string().required(),
    groupId: Joi.string().guid({ version: "uuidv4" }).required(),
  }),
  update: Joi.object().keys({
    // userId: Joi.string().guid({ version: "uuidv4" }),
    title: Joi.string(),
    reason: Joi.string(),
    status: Joi.string(),
    groupId: Joi.string().guid({ version: "uuidv4" }),
  }),
};
