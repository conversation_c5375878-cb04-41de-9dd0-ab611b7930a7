/**
 * @swagger
 * components:
 *   schemas:
 *     questionoption:
 *       type: object
 *       required:
 *         - .required()
 *       properties:
 *         id:
           type: string
           description: The id of the questionoption
         defaultValue:
           type: string
           description: The defaultValue of the questionoption
         allowNull:
           type: string
           description: The allowNull of the questionoption
         primaryKey:
           type: string
           description: The primaryKey of the questionoption
         :
           type: string
           description: The  of the questionoption
 *       example:
 *         id: "Example id"
         defaultValue: "Example defaultValue"
         allowNull: "Example allowNull"
         primaryKey: "Example primaryKey"
         : "Example "
 *
 *     CreatequestionoptionRequest:
 *       type: object
 *       required:
 *         - .required()
 *       properties:
 *         optionText:
           type: string
         isCorrect:
           type: string
         questionId:
           type: string
 *       example:
 *         optionText: "Example optionText"
         isCorrect: "Example isCorrect"
         questionId: "Example questionId"
 *
 *     UpdatequestionoptionRequest:
 *       type: object
 *       properties:
 *         optionText:
           type: string
         isCorrect:
           type: string
         questionId:
           type: string
 *       example:
 *         optionText: "Example optionText"
         isCorrect: "Example isCorrect"
         questionId: "Example questionId"
 *
 *   parameters:
 *     questionoptionId:
 *       in: path
 *       name: id
 *       required: true
 *       schema:
 *         type: string
 *         format: uuid
 *       description: The questionoption ID
 *
 *   responses:
 *     UnauthorizedError:
 *       description: Access token is missing or invalid
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: integer
 *                 example: 401
 *               message:
 *                 type: string
 *                 example: Unauthorized attempt, login again!
 *
 * paths:
 *   /api/v1/questionoption:
 *     get:
 *       summary: Get all questionoptions
 *       tags: [questionoptions]
 *       parameters:
 *         - in: query
 *           name: page
 *           schema:
 *             type: integer
 *             default: 1
 *           description: Page number for pagination
 *         - in: query
 *           name: limit
 *           schema:
 *             type: integer
 *             default: 10
 *           description: Number of items per page
 *       responses:
 *         200:
 *           description: List of questionoptions
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: questionoptions retrieved successfully
 *                   data:
 *                     type: object
 *                     properties:
 *                       questionoptions:
 *                         type: array
 *                         items:
 *                           $ref: '#/components/schemas/questionoption'
 *                       pagination:
 *                         type: object
 *                         properties:
 *                           total:
 *                             type: integer
 *                             description: Total number of questionoptions
 *                           page:
 *                             type: integer
 *                             description: Current page number
 *                           limit:
 *                             type: integer
 *                             description: Number of items per page
 *                           pages:
 *                             type: integer
 *                             description: Total number of pages
 *
 *   /api/v1/questionoption/{id}:
 *     get:
 *       summary: Get questionoption by ID
 *       tags: [questionoptions]
 *       parameters:
 *         - $ref: '#/components/parameters/questionoptionId'
 *       responses:
 *         200:
 *           description: questionoption retrieved successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: questionoption retrieved successfully
 *                   data:
 *                     $ref: '#/components/schemas/questionoption'
 *         404:
 *           description: questionoption not found
 *
 *   /api/v1/admin/questionoption:
 *     post:
 *       summary: Create a new questionoption
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/CreatequestionoptionRequest'
 *       responses:
 *         201:
 *           description: questionoption created successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 201
 *                   message:
 *                     type: string
 *                     example: questionoption created successfully
 *                   data:
 *                     $ref: '#/components/schemas/questionoption'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *
 *   /api/v1/admin/questionoption/{id}:
 *     patch:
 *       summary: Update a questionoption
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/questionoptionId'
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/UpdatequestionoptionRequest'
 *       responses:
 *         200:
 *           description: questionoption updated successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: questionoption updated successfully
 *                   data:
 *                     $ref: '#/components/schemas/questionoption'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: questionoption not found
 *
 *     delete:
 *       summary: Delete a questionoption
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/questionoptionId'
 *       responses:
 *         200:
 *           description: questionoption deleted successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: questionoption deleted successfully
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: questionoption not found
 */