"use strict";
const { DataTypes } = require("sequelize");
const { v4: uuidv4 } = require('uuid');
const sequelize = require("../../config/db");
const Question = require("../question/model");

const QuestionOption = sequelize.define(
  "questionOption",
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: uuidv4,
      allowNull: false,
      primaryKey: true,
    },
    optionText: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    isCorrect: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
  },
  {
    paranoid: true,
  }
);

// Relationships
Question.hasMany(QuestionOption, { foreignKey: { allowNull: false } });
QuestionOption.belongsTo(Question);
module.exports = QuestionOption;
