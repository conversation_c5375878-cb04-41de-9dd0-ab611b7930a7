const Joi = require("joi");

module.exports = {
  create: Joi.object().keys({
    optionText: Joi.string().required(),
    isCorrect: Joi.boolean(),
    questionId: Joi.string().guid({ version: "uuidv4" }).required(),
    // code: Joi.string().required(),
    // englishName: Joi.string().required(),
    // pronunciation: Joi.string().allow(null),
    // flag: Joi.string().allow(null),
  }),
  update: Joi.object().keys({
    optionText: Joi.string(),
    isCorrect: Joi.boolean(),
    questionId: Joi.string().guid({ version: "uuidv4" }),


    // code: Joi.string(),
    // englishName: Joi.string(),
    // pronunciation: Joi.string(),
    // flag: Joi.string(),
  }),
};
