"use strict";

const router = require("express").Router();
const questionOption = require("./controller");
const joiSchema = require("./joiSchema");
const { joiValidator } = require("../../middlewares/joiValidator");
// const { authMiddleware, protectRoute } = require("../../middlewares/auth");


router
  .route("/")
  .get(questionOption.getAll)
  .post(joiValidator(joiSchema.create), questionOption.add);
router
  .route("/:id")
  .get(questionOption.getById)
  .patch(joiValidator(joiSchema.update), questionOption.update)
  .delete(questionOption.delete);

module.exports = router;
