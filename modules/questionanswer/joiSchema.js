const Joi = require("joi");

module.exports = {
  create: Joi.object().keys({
    selectedOptionId: Joi.string().guid({ version: "uuidv4" }).required(),
    quizId: Joi.string().guid({ version: "uuidv4" }).required(),
    answerText: Joi.string().required(),
    isCorrect: Joi.boolean(),
  }),
  update: Joi.object().keys({
    selectedOptionId: Joi.string().guid({ version: "uuidv4" }),
    quizId: Joi.string().guid({ version: "uuidv4" }),
    answerText: Joi.string(),
    isCorrect: Joi.boolean(),
  }),
};
