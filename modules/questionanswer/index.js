"use strict";

const router = require("express").Router();
const questionAnswer = require("./controller");
// const { authMiddleware, protectRoute } = require("../../middlewares/auth");
// const { joiValidator } = require("../../middlewares/joiValidator");

router
  .route("/")
  .get(questionAnswer.getAll)

//   .post(joiValidator(joiSchema.create), questionAnswer.add);
router
  .route("/:id")
  .get(questionAnswer.getById)
//   .patch(joiValidator(joiSchema.update), questionAnswer.update)
//   .delete(questionAnswer.delete);

module.exports = router;
