"use strict";
const { DataTypes } = require("sequelize");
const { v4: uuidv4 } = require("uuid");
const sequelize = require("../../config/db");
const User = require("../user/model");
const Question = require("../question/model");

const QuestionAnswer = sequelize.define(
  "questionAnswer",
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: uuidv4,
      allowNull: false,
      primaryKey: true,
    },
    selectedOptionId: {
      type: DataTypes.UUID,
      allowNull: true,
    },
    answerText: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    isCorrect: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
  },
  {
    paranoid: true,
  }
);

// Relationships
User.hasMany(QuestionAnswer, { foreignKey: { allowNull: false } });
QuestionAnswer.belongsTo(User);

Question.hasMany(QuestionAnswer, { foreignKey: { allowNull: false } });
QuestionAnswer.belongsTo(Question);

module.exports = QuestionAnswer;
