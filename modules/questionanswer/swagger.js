/**
 * @swagger
 * components:
 *   schemas:
 *     questionanswer:
 *       type: object
 *       required:
 *         
 *       properties:
 *         id:
           type: string
           description: The id of the questionanswer
         defaultValue:
           type: string
           description: The defaultValue of the questionanswer
         allowNull:
           type: string
           description: The allowNull of the questionanswer
         primaryKey:
           type: string
           description: The primaryKey of the questionanswer
         :
           type: string
           description: The  of the questionanswer
 *       example:
 *         id: "Example id"
         defaultValue: "Example defaultValue"
         allowNull: "Example allowNull"
         primaryKey: "Example primaryKey"
         : "Example "
 *
 *     CreatequestionanswerRequest:
 *       type: object
 *       required:
 *         
 *       properties:
 *         selectedOptionId:
           type: string
 *       example:
 *         selectedOptionId: "Example selectedOptionId"
 *
 *     UpdatequestionanswerRequest:
 *       type: object
 *       properties:
 *         selectedOptionId:
           type: string
 *       example:
 *         selectedOptionId: "Example selectedOptionId"
 *
 *   parameters:
 *     questionanswerId:
 *       in: path
 *       name: id
 *       required: true
 *       schema:
 *         type: string
 *         format: uuid
 *       description: The questionanswer ID
 *
 *   responses:
 *     UnauthorizedError:
 *       description: Access token is missing or invalid
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: integer
 *                 example: 401
 *               message:
 *                 type: string
 *                 example: Unauthorized attempt, login again!
 *
 * paths:
 *   /api/v1/questionanswer:
 *     get:
 *       summary: Get all questionanswers
 *       tags: [questionanswers]
 *       parameters:
 *         - in: query
 *           name: page
 *           schema:
 *             type: integer
 *             default: 1
 *           description: Page number for pagination
 *         - in: query
 *           name: limit
 *           schema:
 *             type: integer
 *             default: 10
 *           description: Number of items per page
 *       responses:
 *         200:
 *           description: List of questionanswers
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: questionanswers retrieved successfully
 *                   data:
 *                     type: object
 *                     properties:
 *                       questionanswers:
 *                         type: array
 *                         items:
 *                           $ref: '#/components/schemas/questionanswer'
 *                       pagination:
 *                         type: object
 *                         properties:
 *                           total:
 *                             type: integer
 *                             description: Total number of questionanswers
 *                           page:
 *                             type: integer
 *                             description: Current page number
 *                           limit:
 *                             type: integer
 *                             description: Number of items per page
 *                           pages:
 *                             type: integer
 *                             description: Total number of pages
 *
 *   /api/v1/questionanswer/{id}:
 *     get:
 *       summary: Get questionanswer by ID
 *       tags: [questionanswers]
 *       parameters:
 *         - $ref: '#/components/parameters/questionanswerId'
 *       responses:
 *         200:
 *           description: questionanswer retrieved successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: questionanswer retrieved successfully
 *                   data:
 *                     $ref: '#/components/schemas/questionanswer'
 *         404:
 *           description: questionanswer not found
 *
 *   /api/v1/admin/questionanswer:
 *     post:
 *       summary: Create a new questionanswer
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/CreatequestionanswerRequest'
 *       responses:
 *         201:
 *           description: questionanswer created successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 201
 *                   message:
 *                     type: string
 *                     example: questionanswer created successfully
 *                   data:
 *                     $ref: '#/components/schemas/questionanswer'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *
 *   /api/v1/admin/questionanswer/{id}:
 *     patch:
 *       summary: Update a questionanswer
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/questionanswerId'
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/UpdatequestionanswerRequest'
 *       responses:
 *         200:
 *           description: questionanswer updated successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: questionanswer updated successfully
 *                   data:
 *                     $ref: '#/components/schemas/questionanswer'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: questionanswer not found
 *
 *     delete:
 *       summary: Delete a questionanswer
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/questionanswerId'
 *       responses:
 *         200:
 *           description: questionanswer deleted successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: questionanswer deleted successfully
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: questionanswer not found
 */