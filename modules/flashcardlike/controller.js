"use strict";
const FlashCardLikeService = require("./service");
const { sqquery } = require("../../utils/query");

exports.getAll = async (req, res, next) => {
  try {
    const data = await FlashCardLikeService.findAndCountAll(sqquery(req.query));

    res.status(200).send({
      status: "success",
      data,
    });
  } catch (error) {
    next(error);
  }
};
exports.add = async (req, res, next) => {
  try {
    const data = await FlashCardLikeService.create(req.body);

    res.status(200).json({
      status: "success",
      data,
    });
  } catch (error) {
    // console.error(error);
    next(error);
  }
};

exports.getById = async (req, res, next) => {
  try {
    const data = await FlashCardLikeService.findOne({
      where: {
        id: req.params.id,
      },
    });

    res.status(200).send({
      status: "success",
      data,
    });
  } catch (error) {
    next(error);
  }
};

exports.update = async (req, res, next) => {
  try {
    const [affectedRows] = await FlashCardLikeService.update(req.body, {
      where: {
        id: req.params.id,
      },
    });

    res.status(200).json({
      status: "success",
      data: {
        affectedRows,
      },
    });
  } catch (error) {
    next(error);
  }
};

exports.delete = async (req, res, next) => {
  try {
    const affectedRows = await FlashCardLikeService.delete({
      where: {
        id: req.params.id,
      },
    });

    res.status(200).send({
      status: "success",
      data: {
        affectedRows,
      },
    });
  } catch (error) {
    next(error);
  }
};
