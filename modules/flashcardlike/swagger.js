/**
 * @swagger
 * components:
 *   schemas:
 *     flashcardlike:
 *       type: object
 *       required:
 *         
 *       properties:
 *         id:
           type: string
           description: The id of the flashcardlike
         defaultValue:
           type: string
           description: The defaultValue of the flashcardlike
         allowNull:
           type: string
           description: The allowNull of the flashcardlike
         primaryKey:
           type: string
           description: The primaryKey of the flashcardlike
         :
           type: string
           description: The  of the flashcardlike
 *       example:
 *         id: "Example id"
         defaultValue: "Example defaultValue"
         allowNull: "Example allowNull"
         primaryKey: "Example primaryKey"
         : "Example "
 *
 *     CreateflashcardlikeRequest:
 *       type: object
 *       required:
 *         
 *       properties:
 *         userId:
           type: string
 *       example:
 *         userId: "Example userId"
 *
 *     UpdateflashcardlikeRequest:
 *       type: object
 *       properties:
 *         userId:
           type: string
 *       example:
 *         userId: "Example userId"
 *
 *   parameters:
 *     flashcardlikeId:
 *       in: path
 *       name: id
 *       required: true
 *       schema:
 *         type: string
 *         format: uuid
 *       description: The flashcardlike ID
 *
 *   responses:
 *     UnauthorizedError:
 *       description: Access token is missing or invalid
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: integer
 *                 example: 401
 *               message:
 *                 type: string
 *                 example: Unauthorized attempt, login again!
 *
 * paths:
 *   /api/v1/flashcardlike:
 *     get:
 *       summary: Get all flashcardlikes
 *       tags: [flashcardlikes]
 *       parameters:
 *         - in: query
 *           name: page
 *           schema:
 *             type: integer
 *             default: 1
 *           description: Page number for pagination
 *         - in: query
 *           name: limit
 *           schema:
 *             type: integer
 *             default: 10
 *           description: Number of items per page
 *       responses:
 *         200:
 *           description: List of flashcardlikes
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: flashcardlikes retrieved successfully
 *                   data:
 *                     type: object
 *                     properties:
 *                       flashcardlikes:
 *                         type: array
 *                         items:
 *                           $ref: '#/components/schemas/flashcardlike'
 *                       pagination:
 *                         type: object
 *                         properties:
 *                           total:
 *                             type: integer
 *                             description: Total number of flashcardlikes
 *                           page:
 *                             type: integer
 *                             description: Current page number
 *                           limit:
 *                             type: integer
 *                             description: Number of items per page
 *                           pages:
 *                             type: integer
 *                             description: Total number of pages
 *
 *   /api/v1/flashcardlike/{id}:
 *     get:
 *       summary: Get flashcardlike by ID
 *       tags: [flashcardlikes]
 *       parameters:
 *         - $ref: '#/components/parameters/flashcardlikeId'
 *       responses:
 *         200:
 *           description: flashcardlike retrieved successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: flashcardlike retrieved successfully
 *                   data:
 *                     $ref: '#/components/schemas/flashcardlike'
 *         404:
 *           description: flashcardlike not found
 *
 *   /api/v1/admin/flashcardlike:
 *     post:
 *       summary: Create a new flashcardlike
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/CreateflashcardlikeRequest'
 *       responses:
 *         201:
 *           description: flashcardlike created successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 201
 *                   message:
 *                     type: string
 *                     example: flashcardlike created successfully
 *                   data:
 *                     $ref: '#/components/schemas/flashcardlike'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *
 *   /api/v1/admin/flashcardlike/{id}:
 *     patch:
 *       summary: Update a flashcardlike
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/flashcardlikeId'
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/UpdateflashcardlikeRequest'
 *       responses:
 *         200:
 *           description: flashcardlike updated successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: flashcardlike updated successfully
 *                   data:
 *                     $ref: '#/components/schemas/flashcardlike'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: flashcardlike not found
 *
 *     delete:
 *       summary: Delete a flashcardlike
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/flashcardlikeId'
 *       responses:
 *         200:
 *           description: flashcardlike deleted successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: flashcardlike deleted successfully
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: flashcardlike not found
 */