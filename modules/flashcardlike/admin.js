"use strict";

const router = require("express").Router();
const flashcardLike = require("./controller");
const joiSchema = require("./joiSchema");
const { joiValidator } = require("../../middlewares/joiValidator");
// const { authMiddleware, protectRoute } = require("../../middlewares/auth");


router
  .route("/")
  .get(flashcardLike.getAll)
  .post(joiValidator(joiSchema.create), flashcardLike.add);
router
  .route("/:id")
  .get(flashcardLike.getById)
  .patch(joiValidator(joiSchema.update), flashcardLike.update)
  .delete(flashcardLike.delete);

module.exports = router;
