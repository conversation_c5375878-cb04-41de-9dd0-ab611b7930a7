"use strict";
const { DataTypes } = require("sequelize");
const { v4: uuidv4 } = require("uuid");
const sequelize = require("../../config/db");
const User = require("../user/model");
const Flashcard = require("../flashcard/model");

const FlashcardLike = sequelize.define("flashcardLike", {
  id: {
    type: DataTypes.UUID,
    defaultValue: uuidv4,
    allowNull: false,
    primaryKey: true,
  },
}, {
  paranoid: true,
});

// Relationships
User.hasMany(FlashcardLike, { foreignKey: { allowNull: false } });
FlashcardLike.belongsTo(User);

Flashcard.hasMany(FlashcardLike, { foreignKey: { allowNull: false } });
FlashcardLike.belongsTo(Flashcard);

module.exports = FlashcardLike;
