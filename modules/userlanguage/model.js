"use strict";
const { DataTypes } = require("sequelize");
const { v4: uuidv4 } = require("uuid");
const sequelize = require("../../config/db");
const User = require("../user/model");
const Language = require("../language/model");

const UserLanguage = sequelize.define(
  "userLanguage",
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: uuidv4,
      allowNull: false,
      primaryKey: true,
    },
  },
  {
    paranoid: true,
  }
);

// Relationships
User.hasMany(UserLanguage, { foreignKey: { allowNull: false } });
UserLanguage.belongsTo(User);

Language.hasMany(UserLanguage, { foreignKey: { allowNull: false } });
UserLanguage.belongsTo(Language);

module.exports = UserLanguage;
