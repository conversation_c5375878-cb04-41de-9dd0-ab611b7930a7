/**
 * @swagger
 * components:
 *   schemas:
 *     userlanguage:
 *       type: object
 *       required:
 *         
 *       properties:
 *         id:
           type: string
           description: The id of the userlanguage
         defaultValue:
           type: string
           description: The defaultValue of the userlanguage
         allowNull:
           type: string
           description: The allowNull of the userlanguage
         primaryKey:
           type: string
           description: The primaryKey of the userlanguage
         :
           type: string
           description: The  of the userlanguage
 *       example:
 *         id: "Example id"
         defaultValue: "Example defaultValue"
         allowNull: "Example allowNull"
         primaryKey: "Example primaryKey"
         : "Example "
 *
 *     CreateuserlanguageRequest:
 *       type: object
 *       required:
 *         
 *       properties:
 *         userId:
           type: string
 *       example:
 *         userId: "Example userId"
 *
 *     UpdateuserlanguageRequest:
 *       type: object
 *       properties:
 *         userId:
           type: string
 *       example:
 *         userId: "Example userId"
 *
 *   parameters:
 *     userlanguageId:
 *       in: path
 *       name: id
 *       required: true
 *       schema:
 *         type: string
 *         format: uuid
 *       description: The userlanguage ID
 *
 *   responses:
 *     UnauthorizedError:
 *       description: Access token is missing or invalid
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: integer
 *                 example: 401
 *               message:
 *                 type: string
 *                 example: Unauthorized attempt, login again!
 *
 * paths:
 *   /api/v1/userlanguage:
 *     get:
 *       summary: Get all userlanguages
 *       tags: [userlanguages]
 *       parameters:
 *         - in: query
 *           name: page
 *           schema:
 *             type: integer
 *             default: 1
 *           description: Page number for pagination
 *         - in: query
 *           name: limit
 *           schema:
 *             type: integer
 *             default: 10
 *           description: Number of items per page
 *       responses:
 *         200:
 *           description: List of userlanguages
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: userlanguages retrieved successfully
 *                   data:
 *                     type: object
 *                     properties:
 *                       userlanguages:
 *                         type: array
 *                         items:
 *                           $ref: '#/components/schemas/userlanguage'
 *                       pagination:
 *                         type: object
 *                         properties:
 *                           total:
 *                             type: integer
 *                             description: Total number of userlanguages
 *                           page:
 *                             type: integer
 *                             description: Current page number
 *                           limit:
 *                             type: integer
 *                             description: Number of items per page
 *                           pages:
 *                             type: integer
 *                             description: Total number of pages
 *
 *   /api/v1/userlanguage/{id}:
 *     get:
 *       summary: Get userlanguage by ID
 *       tags: [userlanguages]
 *       parameters:
 *         - $ref: '#/components/parameters/userlanguageId'
 *       responses:
 *         200:
 *           description: userlanguage retrieved successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: userlanguage retrieved successfully
 *                   data:
 *                     $ref: '#/components/schemas/userlanguage'
 *         404:
 *           description: userlanguage not found
 *
 *   /api/v1/admin/userlanguage:
 *     post:
 *       summary: Create a new userlanguage
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/CreateuserlanguageRequest'
 *       responses:
 *         201:
 *           description: userlanguage created successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 201
 *                   message:
 *                     type: string
 *                     example: userlanguage created successfully
 *                   data:
 *                     $ref: '#/components/schemas/userlanguage'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *
 *   /api/v1/admin/userlanguage/{id}:
 *     patch:
 *       summary: Update a userlanguage
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/userlanguageId'
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/UpdateuserlanguageRequest'
 *       responses:
 *         200:
 *           description: userlanguage updated successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: userlanguage updated successfully
 *                   data:
 *                     $ref: '#/components/schemas/userlanguage'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: userlanguage not found
 *
 *     delete:
 *       summary: Delete a userlanguage
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/userlanguageId'
 *       responses:
 *         200:
 *           description: userlanguage deleted successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: userlanguage deleted successfully
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: userlanguage not found
 */