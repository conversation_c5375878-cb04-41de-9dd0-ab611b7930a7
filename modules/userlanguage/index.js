"use strict";

const router = require("express").Router();
const userLanguage = require("./controller");
// const { authMiddleware, protectRoute } = require("../../middlewares/auth");
// const { joiValidator } = require("../../middlewares/joiValidator");

router
  .route("/")
  .get(userLanguage.getAll)

//   .post(joiValidator(joiSchema.create), userLanguage.add);
router
  .route("/:id")
  .get(userLanguage.getById)
//   .patch(joiValidator(joiSchema.update), userLanguage.update)
//   .delete(userLanguage.delete);

module.exports = router;
