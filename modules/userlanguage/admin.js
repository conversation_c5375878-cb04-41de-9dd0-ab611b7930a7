"use strict";

const router = require("express").Router();
const UserLanguage = require("./controller");
const joiSchema = require("./joiSchema");
const { joiValidator } = require("../../middlewares/joiValidator");
// const { authMiddleware, protectRoute } = require("../../middlewares/auth");


router
  .route("/")
  .get(UserLanguage.getAll)
  .post(joiValidator(joiSchema.create), UserLanguage.add);
router
  .route("/:id")
  .get(UserLanguage.getById)
  .patch(joiValidator(joiSchema.update), UserLanguage.update)
  .delete(UserLanguage.delete);

module.exports = router;
