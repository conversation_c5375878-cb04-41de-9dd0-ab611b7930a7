"use strict";
const LogService = require("./service");
const { sqquery } = require("../../utils/query");

exports.getAll = async (req, res, next) => {
  try {
    const data = await LogService.findAndCountAll(sqquery(req.query));

    res.status(200).send({
      status: "success",

      data,
    });
  } catch (error) {
    next(error);
  }
};

exports.getById = async (req, res, next) => {
  try {
    const data = await LogService.findOne({
      where: {
        id: req.params.id,
      },
    });

    res.status(200).send({
      status: "success",
      data,
    });
  } catch (error) {
    next(error);
  }
};

exports.delete = async (req, res, next) => {
  try {
    const affectedRows = await LogService.delete({
      where: {
        id: req.params.id,
      },
    });

    res.status(200).send({
      status: "success",
      data: {
        affectedRows,
      },
    });
  } catch (error) {
    next(error);
  }
};
