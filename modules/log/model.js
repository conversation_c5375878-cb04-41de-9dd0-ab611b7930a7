"use strict";
const { DataTypes } = require("sequelize");
const { v4: uuidv4 } = require('uuid');
const sequelize = require("../../config/db");
const User = require("../user/model");
const Log = sequelize.define("log", {
  id: {
    type: DataTypes.UUID,
    defaultValue: uuidv4,
    allowNull: false,
    primaryKey: true
  },
  method: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  url: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  payload: {
    type: DataTypes.TEXT,
    allowNull: true,
    get: function () {
      return JSON.parse(this.getDataValue("payload"));
    },
    set: function (val) {
      return this.setDataValue("payload", JSON.stringify(val));
    },
  },
  statusCode: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  message: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  stack: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
});

User.hasMany(Log);
Log.belongsTo(User);
module.exports = Log;
