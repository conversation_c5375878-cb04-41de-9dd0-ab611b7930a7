"use strict";
const { DataTypes } = require("sequelize");
const { v4: uuidv4 } = require("uuid");
const sequelize = require("../../config/db");
const User = require("../user/model");
const Flashcard = require("../flashcard/model");

const FlashcardBookmark = sequelize.define("flashcardBookmark", {
  id: {
    type: DataTypes.UUID,
    defaultValue: uuidv4,
    allowNull: false,
    primaryKey: true,
  },
}, {
  paranoid: true,
});

// Relationships
User.hasMany(FlashcardBookmark, { foreignKey: { allowNull: false } });
FlashcardBookmark.belongsTo(User);

Flashcard.hasMany(FlashcardBookmark, { foreignKey: { allowNull: false } });
FlashcardBookmark.belongsTo(Flashcard);

module.exports = FlashcardBookmark;
