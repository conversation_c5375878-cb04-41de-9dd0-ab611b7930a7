/**
 * @swagger
 * components:
 *   schemas:
 *     flashcardbookmark:
 *       type: object
 *       required:
 *         
 *       properties:
 *         id:
           type: string
           description: The id of the flashcardbookmark
         defaultValue:
           type: string
           description: The defaultValue of the flashcardbookmark
         allowNull:
           type: string
           description: The allowNull of the flashcardbookmark
         primaryKey:
           type: string
           description: The primaryKey of the flashcardbookmark
         :
           type: string
           description: The  of the flashcardbookmark
 *       example:
 *         id: "Example id"
         defaultValue: "Example defaultValue"
         allowNull: "Example allowNull"
         primaryKey: "Example primaryKey"
         : "Example "
 *
 *     CreateflashcardbookmarkRequest:
 *       type: object
 *       required:
 *         
 *       properties:
 *         userId:
           type: string
 *       example:
 *         userId: "Example userId"
 *
 *     UpdateflashcardbookmarkRequest:
 *       type: object
 *       properties:
 *         userId:
           type: string
 *       example:
 *         userId: "Example userId"
 *
 *   parameters:
 *     flashcardbookmarkId:
 *       in: path
 *       name: id
 *       required: true
 *       schema:
 *         type: string
 *         format: uuid
 *       description: The flashcardbookmark ID
 *
 *   responses:
 *     UnauthorizedError:
 *       description: Access token is missing or invalid
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: integer
 *                 example: 401
 *               message:
 *                 type: string
 *                 example: Unauthorized attempt, login again!
 *
 * paths:
 *   /api/v1/flashcardbookmark:
 *     get:
 *       summary: Get all flashcardbookmarks
 *       tags: [flashcardbookmarks]
 *       parameters:
 *         - in: query
 *           name: page
 *           schema:
 *             type: integer
 *             default: 1
 *           description: Page number for pagination
 *         - in: query
 *           name: limit
 *           schema:
 *             type: integer
 *             default: 10
 *           description: Number of items per page
 *       responses:
 *         200:
 *           description: List of flashcardbookmarks
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: flashcardbookmarks retrieved successfully
 *                   data:
 *                     type: object
 *                     properties:
 *                       flashcardbookmarks:
 *                         type: array
 *                         items:
 *                           $ref: '#/components/schemas/flashcardbookmark'
 *                       pagination:
 *                         type: object
 *                         properties:
 *                           total:
 *                             type: integer
 *                             description: Total number of flashcardbookmarks
 *                           page:
 *                             type: integer
 *                             description: Current page number
 *                           limit:
 *                             type: integer
 *                             description: Number of items per page
 *                           pages:
 *                             type: integer
 *                             description: Total number of pages
 *
 *   /api/v1/flashcardbookmark/{id}:
 *     get:
 *       summary: Get flashcardbookmark by ID
 *       tags: [flashcardbookmarks]
 *       parameters:
 *         - $ref: '#/components/parameters/flashcardbookmarkId'
 *       responses:
 *         200:
 *           description: flashcardbookmark retrieved successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: flashcardbookmark retrieved successfully
 *                   data:
 *                     $ref: '#/components/schemas/flashcardbookmark'
 *         404:
 *           description: flashcardbookmark not found
 *
 *   /api/v1/admin/flashcardbookmark:
 *     post:
 *       summary: Create a new flashcardbookmark
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/CreateflashcardbookmarkRequest'
 *       responses:
 *         201:
 *           description: flashcardbookmark created successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 201
 *                   message:
 *                     type: string
 *                     example: flashcardbookmark created successfully
 *                   data:
 *                     $ref: '#/components/schemas/flashcardbookmark'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *
 *   /api/v1/admin/flashcardbookmark/{id}:
 *     patch:
 *       summary: Update a flashcardbookmark
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/flashcardbookmarkId'
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/UpdateflashcardbookmarkRequest'
 *       responses:
 *         200:
 *           description: flashcardbookmark updated successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: flashcardbookmark updated successfully
 *                   data:
 *                     $ref: '#/components/schemas/flashcardbookmark'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: flashcardbookmark not found
 *
 *     delete:
 *       summary: Delete a flashcardbookmark
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/flashcardbookmarkId'
 *       responses:
 *         200:
 *           description: flashcardbookmark deleted successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: flashcardbookmark deleted successfully
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: flashcardbookmark not found
 */