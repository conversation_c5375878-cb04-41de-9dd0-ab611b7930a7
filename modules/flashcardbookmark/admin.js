"use strict";

const router = require("express").Router();
const flashcardBookmark = require("./controller");
const joiSchema = require("./joiSchema");
const { joiValidator } = require("../../middlewares/joiValidator");
// const { authMiddleware, protectRoute } = require("../../middlewares/auth");


router
  .route("/")
  .get(flashcardBookmark.getAll)
  .post(joiValidator(joiSchema.create), flashcardBookmark.add);
router
  .route("/:id")
  .get(flashcardBookmark.getById)
  .patch(joiValidator(joiSchema.update), flashcardBookmark.update)
  .delete(flashcardBookmark.delete);

module.exports = router;
