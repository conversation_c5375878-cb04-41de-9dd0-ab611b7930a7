/**
 * @swagger
 * components:
 *   schemas:
 *     usergroup:
 *       type: object
 *       required:
 *         - .required()
 *       properties:
 *         id:
           type: string
           description: The id of the usergroup
         defaultValue:
           type: string
           description: The defaultValue of the usergroup
         allowNull:
           type: string
           description: The allowNull of the usergroup
         primaryKey:
           type: string
           description: The primaryKey of the usergroup
         :
           type: string
           description: The  of the usergroup
 *       example:
 *         id: "Example id"
         defaultValue: "Example defaultValue"
         allowNull: "Example allowNull"
         primaryKey: "Example primaryKey"
         : "Example "
 *
 *     CreateusergroupRequest:
 *       type: object
 *       required:
 *         - .required()
 *       properties:
 *         progress:
           type: string
         userId:
           type: string
 *       example:
 *         progress: "Example progress"
         userId: "Example userId"
 *
 *     UpdateusergroupRequest:
 *       type: object
 *       properties:
 *         progress:
           type: string
         userId:
           type: string
 *       example:
 *         progress: "Example progress"
         userId: "Example userId"
 *
 *   parameters:
 *     usergroupId:
 *       in: path
 *       name: id
 *       required: true
 *       schema:
 *         type: string
 *         format: uuid
 *       description: The usergroup ID
 *
 *   responses:
 *     UnauthorizedError:
 *       description: Access token is missing or invalid
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: integer
 *                 example: 401
 *               message:
 *                 type: string
 *                 example: Unauthorized attempt, login again!
 *
 * paths:
 *   /api/v1/usergroup:
 *     get:
 *       summary: Get all usergroups
 *       tags: [usergroups]
 *       parameters:
 *         - in: query
 *           name: page
 *           schema:
 *             type: integer
 *             default: 1
 *           description: Page number for pagination
 *         - in: query
 *           name: limit
 *           schema:
 *             type: integer
 *             default: 10
 *           description: Number of items per page
 *       responses:
 *         200:
 *           description: List of usergroups
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: usergroups retrieved successfully
 *                   data:
 *                     type: object
 *                     properties:
 *                       usergroups:
 *                         type: array
 *                         items:
 *                           $ref: '#/components/schemas/usergroup'
 *                       pagination:
 *                         type: object
 *                         properties:
 *                           total:
 *                             type: integer
 *                             description: Total number of usergroups
 *                           page:
 *                             type: integer
 *                             description: Current page number
 *                           limit:
 *                             type: integer
 *                             description: Number of items per page
 *                           pages:
 *                             type: integer
 *                             description: Total number of pages
 *
 *   /api/v1/usergroup/{id}:
 *     get:
 *       summary: Get usergroup by ID
 *       tags: [usergroups]
 *       parameters:
 *         - $ref: '#/components/parameters/usergroupId'
 *       responses:
 *         200:
 *           description: usergroup retrieved successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: usergroup retrieved successfully
 *                   data:
 *                     $ref: '#/components/schemas/usergroup'
 *         404:
 *           description: usergroup not found
 *
 *   /api/v1/admin/usergroup:
 *     post:
 *       summary: Create a new usergroup
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/CreateusergroupRequest'
 *       responses:
 *         201:
 *           description: usergroup created successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 201
 *                   message:
 *                     type: string
 *                     example: usergroup created successfully
 *                   data:
 *                     $ref: '#/components/schemas/usergroup'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *
 *   /api/v1/admin/usergroup/{id}:
 *     patch:
 *       summary: Update a usergroup
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/usergroupId'
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/UpdateusergroupRequest'
 *       responses:
 *         200:
 *           description: usergroup updated successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: usergroup updated successfully
 *                   data:
 *                     $ref: '#/components/schemas/usergroup'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: usergroup not found
 *
 *     delete:
 *       summary: Delete a usergroup
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/usergroupId'
 *       responses:
 *         200:
 *           description: usergroup deleted successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: usergroup deleted successfully
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: usergroup not found
 */