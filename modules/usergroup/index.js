"use strict";

const router = require("express").Router();
const userGroup = require("./controller");
// const { authMiddleware, protectRoute } = require("../../middlewares/auth");
// const { joiValidator } = require("../../middlewares/joiValidator");

router
  .route("/")
  .get(userGroup.getAll)

//   .post(joiValidator(joiSchema.create), userGroup.add);
router
  .route("/:id")
  .get(userGroup.getById)
//   .patch(joiValidator(joiSchema.update), userGroup.update)
//   .delete(userGroup.delete);

module.exports = router;
