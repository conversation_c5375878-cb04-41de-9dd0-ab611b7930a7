"use strict";
const { DataTypes } = require("sequelize");
const { v4: uuidv4 } = require("uuid");
const sequelize = require("../../config/db");
const User = require("../user/model");
const Group = require("../group/model");

const UserGroup = sequelize.define(
  "userGroup",
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: uuidv4,
      allowNull: false,
      primaryKey: true,
    },
    progress: {
      type: DataTypes.FLOAT,
      defaultValue: 0,
    },
  },
  {
    paranoid: true,
  }
);

// Relationships
User.hasMany(UserGroup, { foreignKey: { allowNull: false } });
UserGroup.belongsTo(User);

Group.hasMany(UserGroup, { foreignKey: { allowNull: false } });
UserGroup.belongsTo(Group);

module.exports = UserGroup;
