const Joi = require("joi");

module.exports = {
  create: Joi.object().keys({
    progress: Joi.number().required(),
    userId: Joi.string().guid({ version: "uuidv4" }).required(),
    groupId: Joi.string().guid({ version: "uuidv4" }).required(),
  }),
  update: Joi.object().keys({
    progress: Joi.number(),
    userId: Joi.string().guid({ version: "uuidv4" }),
    groupId: Joi.string().guid({ version: "uuidv4" }),
  }),
};
