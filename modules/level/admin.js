"use strict";

const router = require("express").Router();
const level = require("./controller");
const joiSchema = require("./joiSchema");
const { joiValidator } = require("../../middlewares/joiValidator");
// const { authMiddleware, protectRoute } = require("../../middlewares/auth");


router
  .route("/")
  .get(level.getAll)
  .post(joiValidator(joiSchema.create), level.add);
router
  .route("/:id")
  .get(level.getById)
  .patch(joiValidator(joiSchema.update), level.update)
  .delete(level.delete);

module.exports = router;
