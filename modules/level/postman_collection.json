{"info": {"name": "Level API", "description": "Collection for Level module APIs", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Get All Levels", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/levels?page=1&limit=10", "host": ["{{base_url}}"], "path": ["api", "v1", "levels"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "name", "value": "", "disabled": true}, {"key": "description", "value": "", "disabled": true}]}, "description": "Get all levels with pagination and filtering options"}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/levels"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"status": "success", "data": {"rows": [{"id": "123e4567-e89b-12d3-a456-426614174000", "name": "<PERSON><PERSON><PERSON>", "description": "Basic level for beginners", "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "deletedAt": null}], "count": 1}}}]}, {"name": "Create Level", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Beginner\",\n    \"description\": \"Basic level for beginners\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/levels", "host": ["{{base_url}}"], "path": ["api", "v1", "levels"]}, "description": "Create a new level"}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Beginner\",\n    \"description\": \"Basic level for beginners\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/levels"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"status": "success", "data": {"id": "123e4567-e89b-12d3-a456-426614174000", "name": "<PERSON><PERSON><PERSON>", "description": "Basic level for beginners", "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "deletedAt": null}}}]}, {"name": "Get Level by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/levels/{{level_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "levels", "{{level_id}}"]}, "description": "Get a specific level by ID"}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/levels/123e4567-e89b-12d3-a456-426614174000"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"status": "success", "data": {"id": "123e4567-e89b-12d3-a456-426614174000", "name": "<PERSON><PERSON><PERSON>", "description": "Basic level for beginners", "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "deletedAt": null}}}]}, {"name": "Update Level", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated Level\",\n    \"description\": \"Updated description\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/levels/{{level_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "levels", "{{level_id}}"]}, "description": "Update a specific level"}, "response": [{"name": "Success Response", "originalRequest": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated Level\",\n    \"description\": \"Updated description\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/levels/123e4567-e89b-12d3-a456-426614174000"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"status": "success", "data": {"affectedRows": 1}}}]}, {"name": "Delete Level", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/levels/{{level_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "levels", "{{level_id}}"]}, "description": "Delete a specific level"}, "response": [{"name": "Success Response", "originalRequest": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/api/v1/levels/123e4567-e89b-12d3-a456-426614174000"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"status": "success", "data": {"affectedRows": 1}}}]}], "variable": [{"key": "base_url", "value": "http://localhost:3001"}, {"key": "token", "value": "your_jwt_token_here"}, {"key": "level_id", "value": "123e4567-e89b-12d3-a456-426614174000"}]}