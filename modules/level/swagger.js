/**
 * @swagger
 * components:
 *   schemas:
 *     level:
 *       type: object
 *       required:
 *         - .required()
         - .required()
 *       properties:
 *         id:
           type: string
           description: The id of the level
         defaultValue:
           type: string
           description: The defaultValue of the level
         allowNull:
           type: string
           description: The allowNull of the level
         primaryKey:
           type: string
           description: The primaryKey of the level
         :
           type: string
           description: The  of the level
 *       example:
 *         id: "Example id"
         defaultValue: "Example defaultValue"
         allowNull: "Example allowNull"
         primaryKey: "Example primaryKey"
         : "Example "
 *
 *     CreatelevelRequest:
 *       type: object
 *       required:
 *         - .required()
         - .required()
 *       properties:
 *         name:
           type: string
         description:
           type: string
         :
           type: string
 *       example:
 *         name: "Example name"
         description: "Example description"
         : "Example "
 *
 *     UpdatelevelRequest:
 *       type: object
 *       properties:
 *         name:
           type: string
         description:
           type: string
         :
           type: string
 *       example:
 *         name: "Example name"
         description: "Example description"
         : "Example "
 *
 *   parameters:
 *     levelId:
 *       in: path
 *       name: id
 *       required: true
 *       schema:
 *         type: string
 *         format: uuid
 *       description: The level ID
 *
 *   responses:
 *     UnauthorizedError:
 *       description: Access token is missing or invalid
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: integer
 *                 example: 401
 *               message:
 *                 type: string
 *                 example: Unauthorized attempt, login again!
 *
 * paths:
 *   /api/v1/level:
 *     get:
 *       summary: Get all levels
 *       tags: [levels]
 *       parameters:
 *         - in: query
 *           name: page
 *           schema:
 *             type: integer
 *             default: 1
 *           description: Page number for pagination
 *         - in: query
 *           name: limit
 *           schema:
 *             type: integer
 *             default: 10
 *           description: Number of items per page
 *       responses:
 *         200:
 *           description: List of levels
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: levels retrieved successfully
 *                   data:
 *                     type: object
 *                     properties:
 *                       levels:
 *                         type: array
 *                         items:
 *                           $ref: '#/components/schemas/level'
 *                       pagination:
 *                         type: object
 *                         properties:
 *                           total:
 *                             type: integer
 *                             description: Total number of levels
 *                           page:
 *                             type: integer
 *                             description: Current page number
 *                           limit:
 *                             type: integer
 *                             description: Number of items per page
 *                           pages:
 *                             type: integer
 *                             description: Total number of pages
 *
 *   /api/v1/level/{id}:
 *     get:
 *       summary: Get level by ID
 *       tags: [levels]
 *       parameters:
 *         - $ref: '#/components/parameters/levelId'
 *       responses:
 *         200:
 *           description: level retrieved successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: level retrieved successfully
 *                   data:
 *                     $ref: '#/components/schemas/level'
 *         404:
 *           description: level not found
 *
 *   /api/v1/admin/level:
 *     post:
 *       summary: Create a new level
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/CreatelevelRequest'
 *       responses:
 *         201:
 *           description: level created successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 201
 *                   message:
 *                     type: string
 *                     example: level created successfully
 *                   data:
 *                     $ref: '#/components/schemas/level'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *
 *   /api/v1/admin/level/{id}:
 *     patch:
 *       summary: Update a level
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/levelId'
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/UpdatelevelRequest'
 *       responses:
 *         200:
 *           description: level updated successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: level updated successfully
 *                   data:
 *                     $ref: '#/components/schemas/level'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: level not found
 *
 *     delete:
 *       summary: Delete a level
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/levelId'
 *       responses:
 *         200:
 *           description: level deleted successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: level deleted successfully
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: level not found
 */