"use strict";

const router = require("express").Router();
const level = require("./controller");
// const { authMiddleware, protectRoute } = require("../../middlewares/auth");
// const { joiValidator } = require("../../middlewares/joiValidator");

router
  .route("/")
  .get(level.getAll)

//   .post(joiValidator(joiSchema.create), level.add);
router
  .route("/:id")
  .get(level.getById)
//   .patch(joiValidator(joiSchema.update), level.update)
//   .delete(level.delete);

module.exports = router;
