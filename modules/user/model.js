"use strict";
const { DataTypes } = require("sequelize");
const sequelize = require("../../config/db");
const bcryptjs = require("bcryptjs");
const { v4: uuidv4 } = require('uuid');
const User = sequelize.define(
  "user",
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: uuidv4,
      allowNull: false,
      primaryKey: true
    },
    username: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
      isEmail: true,
    },
    password: {
      type: DataTypes.STRING,
      // allowNull: false,
    },
    role : {
      type : DataTypes.STRING,
      allowNull : false
    },
    uid: {
      type: DataTypes.STRING,
    },
    profilePic: {
      type: DataTypes.STRING,
    },
    FCM: {
      type: DataTypes.STRING,
    },
    isBlocked: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    isPremium: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
  },
  {
    paranoid: true,
    uniqueKeys: {
      email: {
        fields: ["email"],
      },
    },
    hooks: {
      beforeCreate: async (user, options) => {
        console.log("before save/create User");
        if (user.password)
          user.password = await bcryptjs.hash(user.password, 12);
      },
      beforeUpdate: async (user, options) => {
        console.log("Before update User");
        if (user.password)
          user.password = await bcryptjs.hash(user.password, 12);
      },
    },
  }
);

module.exports = User;
