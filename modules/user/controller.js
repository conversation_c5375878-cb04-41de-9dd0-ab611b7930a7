"use strict";
const sequelize = require("../../config/db");
const bcryptjs = require("bcryptjs");
const crypto = require("crypto");
const client = require("../../config/OAuthGoogle");
const createError = require("http-errors");
const UserService = require("./service");
const redisService = require("../../utils/redis");
const { usersqquery, sqquery } = require("../../utils/query");
const { deleteFiles } = require("../../middlewares/multer");
const { sendOTP } = require("../../utils/mail");
const { generateProfilePic } = require("../../middlewares/generateProfile");
const {
  getJwtToken,
  generateOTP,
  jwtDecoderForBody,
  createFirebaseUser,
  verifyFirebaseUserToken,
  deleteFirebaseUser,
} = require("../../utils/service");
const { userAllAdminAttributes } = require("../../constants/queryAttributes");
const { ROLES } = require("../../middlewares/auth");

// Signup route
exports.signup = async (req, res, next) => {
  try {
    const { username, email, password } = req.body;

    // Check if the email already exists in the local database
    const existingUser = await UserService.findOne({
      where: { email },
    });

    if (existingUser) {
      throw createError(400, `${email} already registered`);
    }

    // Generate a 6-digit OTP
    const OTP = generateOTP();

    // Store OTP and Password temporary in Redis with a key associated with the user's email
    await Promise.all([
      redisService.set(email, OTP, 300), // Set expiration time to 300 seconds (5 minutes)
      redisService.set(`${email}-pass`, password, 300), // Set expiration time to 300 seconds (5 minutes)
      sendOTP({ email, username, OTP }), // Send the email with the OTP
    ]);
    // Generate JWT token and send response
    const token = await getJwtToken({ username, email });

    res.status(200).json({
      status: "success",
      message: `OTP sent to ${email}`,
      token,
    });
  } catch (error) {
    console.error(error);
    next(error);
  }
};

// OTP verification route
exports.verifyOTP = async (req, res, next) => {
  try {
    const decodedToken = await jwtDecoderForBody(req.body.token);

    if (!decodedToken || !decodedToken.username || !decodedToken.email) {
      throw createError(400, "Invalid token or missing data!");
    }

    const [storedOTP, storedPASS] = await Promise.all([
      redisService.get(decodedToken.email),
      redisService.get(`${decodedToken.email}-pass`),
    ]);

    // Compare the entered OTP with the OTP from the token
    const isOTPValid = crypto.timingSafeEqual(
      Buffer.from(req.body.otp.toString()),
      Buffer.from(storedOTP.toString())
    );

    if (!isOTPValid) {
      throw createError(400, "Invalid OTP");
    }

    // Create the user in Firebase Authentication
    const firebaseUser = await createFirebaseUser(decodedToken);
    // console.log("firebaseUser : ", firebaseUser);

    // Generate the profile picture URL using the username
    let profilePicUrl;
    try {
      profilePicUrl = await generateProfilePic(decodedToken.username);
    } catch (error) {
      console.error("Error generating profile picture:", error);
      profilePicUrl = `${process.env.CDN_URL}/logo/ai_profile.png`;
    }

    // Get the user's UUID & profilePic from Firebase User
    const uid = firebaseUser.uid;
    const profilePic = firebaseUser.photoURL || profilePicUrl;

    // Create the user in your local database
    const user = await UserService.create({
      username: decodedToken.username,
      email: decodedToken.email,
      password: storedPASS,
      uid,
      profilePic,
    });

    // Generate JWT token and send response
    const token = await getJwtToken({ id: user.id, role: ROLES.USER });

    res.status(200).json({
      status: "success",
      message: "Signup successful",
      token,
      user,
    });

    redisService.del(decodedToken.email);
    redisService.del(`${decodedToken.email}-pass`);
  } catch (error) {
    next(error);
  }
};

exports.socialAuth = async (req, res, next) => {
  try {
    const { firebase_token } = req.body;
    // console.log("firebase-Token: ", firebase_token);

    if (!firebase_token) {
      throw createError(400, "Invalid request. Missing firebase_token.");
    }

    // Verify the user exist in Firebase and token is perfect
    const firebaseUser = await verifyFirebaseUserToken(firebase_token);
    // console.log("firebaseUser : social : ", firebaseUser);

    if (!firebaseUser || !firebaseUser.email) {
      throw createError(400, "Invalid firebase_token or missing email.");
    }
    const { email, name, uid, picture } = firebaseUser;

    let user = await UserService.findOne({ where: { email } });

    if (!user) {
      // Use picture if it exists, otherwise generate the profile picture URL
      const profilePic =
        picture ||
        (await generateProfilePic(name)) ||
        `${process.env.CDN_URL}/logo/ai_profile.png`;

      // Create the user in the local database
      user = await UserService.create({
        username: name,
        email,
        uid,
        profilePic,
        role: ROLES.USER,
      });
    }

    if (user.isBlocked) {
      return res.status(401).json({
        status: "Permission Denied",
        message: "You're Blocked by Admin",
      });
    }

    const token = await getJwtToken({
      id: user.id,
      email: user.email,
      role: user.role,
    });

    res.status(200).json({
      status: 200,
      message: "Login successful",
      token,
    });
  } catch (error) {
    console.error(error);
    next(error);
  }
};
exports.googleOAuth = async (req, res, next) => {
  try {
    const { token } = req.body;

    const authUser = await client.verifyToken(token);

    const { email, name, picture } = authUser.payload;

    let user = await UserService.findOne({ where: { email } });

    if (!user) {
      // Use picture if it exists, otherwise generate the profile picture URL
      const profilePic =
        picture ||
        (await generateProfilePic(name)) ||
        `${process.env.CDN_URL}/logo/ai_profile.png`;

      // Create the user in the local database
      user = await UserService.create({
        username: name,
        email,
        profilePic,
        role: ROLES.USER,
      });
    }

    if (user.isBlocked) {
      return res.status(401).json({
        status: "Permission Denied",
        message: "You're Blocked by Admin",
      });
    }

    const jwtToken = await getJwtToken({
      id: user.id,
      email: user.email,
      role: user.role,
    });

    res.status(200).json({
      status: 200,
      message: "Login successful",
      token: jwtToken,
    });
  } catch (error) {
    console.error(error);
    next(error);
  }
};

// Login route
exports.login = async (req, res, next) => {
  try {
    const { email, password } = req.body;

    // Find the user in your local database using the email
    const user = await UserService.findOne({ where: { email, role: ROLES.USER } });

    // If the user doesn't exist, return error
    if (!user) {
      throw createError(400, "Invalid email or password");
    }

    // Check if the provided password matches the hashed password in the database
    const correctPassword = await bcryptjs.compare(password, user.password);
    if (!correctPassword) {
      throw createError(400, "Invalid email or password");
    }

    // Check if the user is blocked or not
    if (user.isBlocked == true)
      return res.status(401).json({
        status: "Permission Denied",
        message: "You'are Blocked by Admin",
      });

    // Generate JWT token and send response
    const token = await getJwtToken({ id: user.id, role: user?.role });

    res.status(200).json({
      status: "success",
      message: "Login successful",
      token,
      role: user?.role,
      user,
    });
  } catch (error) {
    next(error);
  }
};

exports.getProfile = async (req, res, next) => {
  try {
    const userId = req.requestor.id;
    const user = await UserService.findOne({
      where: {
        id: userId,
      },
    });
    // Exclude the 'password' field from the user object
    if (user) {
      user.password = undefined;
    }

    res.status(200).send({
      status: "success",
      data: user,
    });
  } catch (error) {
    console.error(error);
    next(error);
  }
};

exports.updateProfile = async (req, res, next) => {
  try {
    const userId = req.requestor.id;
    const { file, body } = req;

    // Remove password from the request body
    delete body.password;

    let oldProfilePic;
    if (file) {
      // Update profile picture location in the request body
      body.profilePic = file.location;

      // Retrieve old user data from the database
      const oldUserData = await UserService.findOne({ where: { id: userId } });
      oldProfilePic = oldUserData?.profilePic;
    }

    // Update user's data in the database
    const [affectedRows] = await UserService.update(body, {
      where: { id: userId },
    });

    // Send success response with the number of affected rows
    res.status(200).json({
      status: "success",
      data: { affectedRows },
    });

    // Delete old profile picture from S3 storage if it exists
    if (file && oldProfilePic) {
      deleteFiles([oldProfilePic]);
    }
  } catch (error) {
    next(error);
  }
};
exports.updateFCM = async (req, res, next) => {
  try {
    const userId = req.requestor.id;

    // Update user's data in the database
    const [affectedRows] = await UserService.update(req.body, {
      where: { id: userId },
    });

    // Send success response with the number of affected rows
    res.status(200).json({
      status: "success",
      data: { affectedRows },
    });
  } catch (error) {
    next(error);
  }
};

// <=============== For Admins ===================>

exports.getAll = async (req, res, next) => {
  try {
    const users = await UserService.findAndCountAll({
      ...sqquery(req.query, {}, ["username", "email","role"]),
      attributes: userAllAdminAttributes,
    });

    res.status(200).send({
      status: "success",
      data: users,
    });
  } catch (error) {
    console.error(error);
    next(error);
  }
};

exports.getById = async (req, res, next) => {
  try {
    const user = await UserService.findOne({
      where: {
        id: req.params.id,
      },
    });

    res.status(200).send({
      status: "success",
      data: user,
    });
  } catch (error) {
    console.error(error);
    next(error);
  }
};
exports.blockUser = async (req, res, next) => {
  try {
    const isBlocked = req.body.isBlocked;

    // Update the user's isBlocked status
    const [affectedRows] = await UserService.update(
      { isBlocked },
      {
        where: {
          id: req.params.id,
        },
      }
    );

    // Generate success message based on isBlocked value
    const message = isBlocked
      ? "User blocked successfully!"
      : "User unblocked successfully!";

    res.status(200).json({
      status: "success",
      message,
    });
  } catch (error) {
    next(error);
  }
};

exports.deleteById = async (req, res, next) => {
  try {
    const user = await UserService.findOne({
      where: {
        id: req.params.id,
      },
    });

    if (!user) {
      return res.status(404).json({
        status: "error",
        message: "User not found!",
      });
    }

    // Now, delete the user from Firebase Authentication
    deleteFirebaseUser(user.uid);

    // Delete the user from your database
    const affectedRows = await UserService.delete({
      where: {
        id: req.params.id,
      },
    });

    // Delete the user's profile picture from S3 (if it exists)
    if (user.profilePic) {
      try {
        deleteFiles([user.profilePic]);
        console.log("Profile picture deleted from S3.");
      } catch (s3Error) {
        console.error("Error deleting profile picture from S3: ", s3Error);
      }
    }

    res.status(200).json({
      status: "success",
      message: "User deleted successfully!",
      data: {
        affectedRows,
      },
    });
  } catch (error) {
    console.error("Error deleting User: ", error);
    next(error);
  }
};

exports.uploadImage = async (req, res, next) => {
  try {
    const cdn = `${process.env.CDN_URL}/submission`;
    const url = `${cdn}/${req.file.key}`;
    // console.log(url);
    // console.log(req.file);

    res.status(200).json({
      status: "success",
      data: url,
    });
  } catch (error) {
    console.error("Error in uploadImage controller:", error);
    next(error);
  }
};

exports.adminlogIn = async (req, res, next) => {
  try {
    const { email, password } = req.body;

    // Find the user in your local database using the email
    const user = await UserService.findOne({ where: { email } });

    // If the user doesn't exist, return error
    if (!user) {
      throw createError(400, "Invalid email or password");
    }

    // Check if the provided password matches the hashed password in the database
    const correctPassword = await bcryptjs.compare(password, user.password);
    if (!correctPassword) {
      throw createError(400, "Invalid email or password");
    }

    // Check if the user is blocked or not
    if (user.isBlocked == true)
      return res.status(401).json({
        status: "Permission Denied",
        message: "You'are Blocked by Admin",
      });

    if (!(user.role === ROLES.ADMIN || user.role === ROLES.SUPERADMIN)) {
      throw createError(400, "Unauthorized Acess");
    }
    // Generate JWT token and send response
    const token = await getJwtToken({ id: user.id, role: user?.role });

    res.status(200).json({
      status: "success",
      message: "Login successful",
      token,
      role: "User",
      user,
    });
  } catch (error) {
    next(error);
  }
};
exports.createAdmin = async (req, res, next) => {
  try {
    const { email, password , role , username } = req.body;

    // Find the user in your local database using the email
    const user = await UserService.findOne({ where: { email } });

    // If the user doesn't exist, return error
    if (user) {
      throw createError(400, "Already User Exist  with same email");
    }
    const profilePic = `${process.env.CDN_URL}/logo/ai_profile.png`;
    // Check if the provided password matches the hashed password in the database
    // const correctPassword = await bcryptjs.compare(password, user.password);
    // if (!correctPassword) {
    //   throw createError(400, "Invalid email or password");
    // }
    const admin = await UserService.create({
      email,
      password,
      username : username ??email,
      uid: "",
      profilePic,
      role: role ?? ROLES.ADMIN,
    });

    // Generate JWT token and send response
    const token = await getJwtToken({ id: admin.id, role: admin.role });

    res.status(200).json({
      status: "success",
      message: "Login successful",
      token,
      user: admin,
    });
  } catch (error) {
    next(error);
  }
};
