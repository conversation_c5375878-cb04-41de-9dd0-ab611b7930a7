"use strict";

const router = require("express").Router();
const user = require("./controller");
const joiSchema = require("./joiSchema");
const { authMiddleware, protectRoute, ROLES } = require("../../middlewares/auth");
const {joiValidator} = require("../../middlewares/joiValidator");
const {upload,deleteFiles} = require("../../middlewares/multer");
const {limiter} = require("../../middlewares/rateLimiter");


// <=============== Public APIs ===============>
router.post("/verify", limiter, user.verifyOTP); 
router.post("/signup",limiter, joiValidator(joiSchema.signup), user.signup); 
router.post("/google",limiter,  user.socialAuth); 
router.post("/oauth",limiter,  user.googleOAuth); 
router.post("/login",limiter, user.login); 
router.post("/upload",upload.single('image') , user.uploadImage);

// <=============== Authorized APIs ===============>
router.use(authMiddleware,protectRoute([ROLES.USER,ROLES.ADMIN,ROLES.SUPERADMIN]));
// <=============== APIs only for Users ===============>
router.get("/me", user.getProfile);
router.patch("/profile", upload.single('profilePic') ,joiValidator(joiSchema.updateProfile), user.updateProfile);
router.patch("/fcm",joiValidator(joiSchema.updateFCM), user.updateFCM);



module.exports = router;
