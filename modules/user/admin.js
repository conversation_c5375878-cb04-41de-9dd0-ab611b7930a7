"use strict";

const router = require("express").Router();
const { authMiddleware, protectRoute, ROLES } = require("../../middlewares/auth");
const { limiter } = require("../../middlewares/rateLimiter");
const user = require("./controller");
router.post("/login", user.adminlogIn); 
router.post("/signup", user.createAdmin); 

router.use(authMiddleware,protectRoute([ROLES.ADMIN,ROLES.SUPERADMIN]));

router.get("/", user.getAll);
router
  .route("/:id")
  .get(user.getById)
  .patch(user.blockUser)
  .delete(user.deleteById);

module.exports = router;
