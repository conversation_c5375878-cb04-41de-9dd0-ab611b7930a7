/**
 * @swagger
 * components:
 *   schemas:
 *     user:
 *       type: object
 *       required:
 *         
 *       properties:
 *         id:
           type: string
           description: The id of the user
         defaultValue:
           type: string
           description: The defaultValue of the user
         allowNull:
           type: string
           description: The allowNull of the user
         primaryKey:
           type: string
           description: The primaryKey of the user
 *       example:
 *         id: "Example id"
         defaultValue: "Example defaultValue"
         allowNull: "Example allowNull"
         primaryKey: "Example primaryKey"
 *
 *     CreateuserRequest:
 *       type: object
 *       required:
 *         
 *       properties:
 *         :
           type: string
 *       example:
 *         : "Example "
 *
 *     UpdateuserRequest:
 *       type: object
 *       properties:
 *         :
           type: string
 *       example:
 *         : "Example "
 *
 *   parameters:
 *     userId:
 *       in: path
 *       name: id
 *       required: true
 *       schema:
 *         type: string
 *         format: uuid
 *       description: The user ID
 *
 *   responses:
 *     UnauthorizedError:
 *       description: Access token is missing or invalid
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: integer
 *                 example: 401
 *               message:
 *                 type: string
 *                 example: Unauthorized attempt, login again!
 *
 * paths:
 *   /api/v1/user:
 *     get:
 *       summary: Get all users
 *       tags: [users]
 *       parameters:
 *         - in: query
 *           name: page
 *           schema:
 *             type: integer
 *             default: 1
 *           description: Page number for pagination
 *         - in: query
 *           name: limit
 *           schema:
 *             type: integer
 *             default: 10
 *           description: Number of items per page
 *       responses:
 *         200:
 *           description: List of users
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: users retrieved successfully
 *                   data:
 *                     type: object
 *                     properties:
 *                       users:
 *                         type: array
 *                         items:
 *                           $ref: '#/components/schemas/user'
 *                       pagination:
 *                         type: object
 *                         properties:
 *                           total:
 *                             type: integer
 *                             description: Total number of users
 *                           page:
 *                             type: integer
 *                             description: Current page number
 *                           limit:
 *                             type: integer
 *                             description: Number of items per page
 *                           pages:
 *                             type: integer
 *                             description: Total number of pages
 *
 *   /api/v1/user/{id}:
 *     get:
 *       summary: Get user by ID
 *       tags: [users]
 *       parameters:
 *         - $ref: '#/components/parameters/userId'
 *       responses:
 *         200:
 *           description: user retrieved successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: user retrieved successfully
 *                   data:
 *                     $ref: '#/components/schemas/user'
 *         404:
 *           description: user not found
 *
 *   /api/v1/admin/user:
 *     post:
 *       summary: Create a new user
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/CreateuserRequest'
 *       responses:
 *         201:
 *           description: user created successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 201
 *                   message:
 *                     type: string
 *                     example: user created successfully
 *                   data:
 *                     $ref: '#/components/schemas/user'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *
 *   /api/v1/admin/user/{id}:
 *     patch:
 *       summary: Update a user
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/userId'
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/UpdateuserRequest'
 *       responses:
 *         200:
 *           description: user updated successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: user updated successfully
 *                   data:
 *                     $ref: '#/components/schemas/user'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: user not found
 *
 *     delete:
 *       summary: Delete a user
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/userId'
 *       responses:
 *         200:
 *           description: user deleted successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: user deleted successfully
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: user not found
 */