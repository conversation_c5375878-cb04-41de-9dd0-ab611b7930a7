"use strict";
const { DataTypes } = require("sequelize");
const { v4: uuidv4 } = require("uuid");
const sequelize = require("../../config/db");
const User = require("../user/model");
const Flashcard = require("../flashcard/model");

const FlashcardDislike = sequelize.define("flashcardDislike", {
  id: {
    type: DataTypes.UUID,
    defaultValue: uuidv4,
    allowNull: false,
    primaryKey: true,
  },
}, {
  paranoid: true,
});

// Relationships
User.hasMany(FlashcardDislike, { foreignKey: { allowNull: false } });
FlashcardDislike.belongsTo(User);

Flashcard.hasMany(FlashcardDislike, { foreignKey: { allowNull: false } });
FlashcardDislike.belongsTo(Flashcard);

module.exports = FlashcardDislike;
