/**
 * @swagger
 * components:
 *   schemas:
 *     flashcarddislike:
 *       type: object
 *       required:
 *         
 *       properties:
 *         id:
           type: string
           description: The id of the flashcarddislike
         defaultValue:
           type: string
           description: The defaultValue of the flashcarddislike
         allowNull:
           type: string
           description: The allowNull of the flashcarddislike
         primaryKey:
           type: string
           description: The primaryKey of the flashcarddislike
         :
           type: string
           description: The  of the flashcarddislike
 *       example:
 *         id: "Example id"
         defaultValue: "Example defaultValue"
         allowNull: "Example allowNull"
         primaryKey: "Example primaryKey"
         : "Example "
 *
 *     CreateflashcarddislikeRequest:
 *       type: object
 *       required:
 *         
 *       properties:
 *         userId:
           type: string
 *       example:
 *         userId: "Example userId"
 *
 *     UpdateflashcarddislikeRequest:
 *       type: object
 *       properties:
 *         userId:
           type: string
 *       example:
 *         userId: "Example userId"
 *
 *   parameters:
 *     flashcarddislikeId:
 *       in: path
 *       name: id
 *       required: true
 *       schema:
 *         type: string
 *         format: uuid
 *       description: The flashcarddislike ID
 *
 *   responses:
 *     UnauthorizedError:
 *       description: Access token is missing or invalid
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: integer
 *                 example: 401
 *               message:
 *                 type: string
 *                 example: Unauthorized attempt, login again!
 *
 * paths:
 *   /api/v1/flashcarddislike:
 *     get:
 *       summary: Get all flashcarddislikes
 *       tags: [flashcarddislikes]
 *       parameters:
 *         - in: query
 *           name: page
 *           schema:
 *             type: integer
 *             default: 1
 *           description: Page number for pagination
 *         - in: query
 *           name: limit
 *           schema:
 *             type: integer
 *             default: 10
 *           description: Number of items per page
 *       responses:
 *         200:
 *           description: List of flashcarddislikes
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: flashcarddislikes retrieved successfully
 *                   data:
 *                     type: object
 *                     properties:
 *                       flashcarddislikes:
 *                         type: array
 *                         items:
 *                           $ref: '#/components/schemas/flashcarddislike'
 *                       pagination:
 *                         type: object
 *                         properties:
 *                           total:
 *                             type: integer
 *                             description: Total number of flashcarddislikes
 *                           page:
 *                             type: integer
 *                             description: Current page number
 *                           limit:
 *                             type: integer
 *                             description: Number of items per page
 *                           pages:
 *                             type: integer
 *                             description: Total number of pages
 *
 *   /api/v1/flashcarddislike/{id}:
 *     get:
 *       summary: Get flashcarddislike by ID
 *       tags: [flashcarddislikes]
 *       parameters:
 *         - $ref: '#/components/parameters/flashcarddislikeId'
 *       responses:
 *         200:
 *           description: flashcarddislike retrieved successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: flashcarddislike retrieved successfully
 *                   data:
 *                     $ref: '#/components/schemas/flashcarddislike'
 *         404:
 *           description: flashcarddislike not found
 *
 *   /api/v1/admin/flashcarddislike:
 *     post:
 *       summary: Create a new flashcarddislike
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/CreateflashcarddislikeRequest'
 *       responses:
 *         201:
 *           description: flashcarddislike created successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 201
 *                   message:
 *                     type: string
 *                     example: flashcarddislike created successfully
 *                   data:
 *                     $ref: '#/components/schemas/flashcarddislike'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *
 *   /api/v1/admin/flashcarddislike/{id}:
 *     patch:
 *       summary: Update a flashcarddislike
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/flashcarddislikeId'
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/UpdateflashcarddislikeRequest'
 *       responses:
 *         200:
 *           description: flashcarddislike updated successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: flashcarddislike updated successfully
 *                   data:
 *                     $ref: '#/components/schemas/flashcarddislike'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: flashcarddislike not found
 *
 *     delete:
 *       summary: Delete a flashcarddislike
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/flashcarddislikeId'
 *       responses:
 *         200:
 *           description: flashcarddislike deleted successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: flashcarddislike deleted successfully
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: flashcarddislike not found
 */