"use strict";

const router = require("express").Router();
const flashcardDislike = require("./controller");
// const { authMiddleware, protectRoute } = require("../../middlewares/auth");
// const { joiValidator } = require("../../middlewares/joiValidator");

router
  .route("/")
  .get(flashcardDislike.getAll)

//   .post(joiValidator(joiSchema.create), flashcardDislike.add);
router
  .route("/:id")
  .get(flashcardDislike.getById)
//   .patch(joiValidator(joiSchema.update), flashcardDislike.update)
//   .delete(flashcardDislike.delete);

module.exports = router;
