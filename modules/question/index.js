"use strict";

const router = require("express").Router();
const question = require("./controller");
// const { authMiddleware, protectRoute } = require("../../middlewares/auth");
// const { joiValidator } = require("../../middlewares/joiValidator");

router
  .route("/")
  .get(question.getAll)

//   .post(joiValidator(joiSchema.create), question.add);
router
  .route("/:id")
  .get(question.getById)
//   .patch(joiValidator(joiSchema.update), question.update)
//   .delete(question.delete);

module.exports = router;
