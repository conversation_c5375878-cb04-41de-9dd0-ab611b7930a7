"use strict";
const { DataTypes } = require("sequelize");
const { v4: uuidv4 } = require("uuid");
const sequelize = require("../../config/db");
const Quiz = require("../quiz/model");

const Question = sequelize.define(
  "question",
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: uuidv4,
      allowNull: false,
      primaryKey: true,
    },
    question: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    questionType: {
      type: DataTypes.ENUM("multiple-choice", "true-false", "fill-in-the-blank"),
      allowNull: false,
    },
    explanation: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
  },
  {
    paranoid: true,
  }
);
Quiz.hasMany(Question, { foreignKey: { allowNull: false } });
Question.belongsTo(Quiz);

module.exports = Question;
