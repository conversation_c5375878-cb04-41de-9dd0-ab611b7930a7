const Joi = require("joi");

module.exports = {
  create: Joi.object().keys({
    question: Joi.string().required(),
    questionType: Joi.string().required(),
    explanation: Joi.string(),
    isActive: Joi.boolean(),
    quizId: Joi.string().guid({ version: "uuidv4" }).required(),
    options: Joi.array().items(
      Joi.object({
        isCorrect: Joi.boolean().required(),
        optionText: Joi.string().required(),
      })
    ).optional(),
  }),
  update: Joi.object().keys({
    question: Joi.string(),
    questionType: Joi.string(),
    explanation: Joi.string(),
    isActive: Joi.boolean(),
    quizId: Joi.string().guid({ version: "uuidv4" }),
  }),
};
