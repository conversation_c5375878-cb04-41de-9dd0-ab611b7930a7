"use strict";
const QuestionService = require("./service");
const { sqquery } = require("../../utils/query");
const QuestionOptionService = require("../questionoption/service");

exports.getAll = async (req, res, next) => {
  try {
    const data = await QuestionService.findAndCountAll(sqquery(req.query));

    res.status(200).send({
      status: "success",
      data,
    });
  } catch (error) {
    next(error);
  }
};
exports.add = async (req, res, next) => {
  try {
    const { options, ...questionData } = req.body;
    const data = await QuestionService.create(questionData);
    if (options && options.length > 0) {
      // Assume QuestionService.addOptions handles adding options related to the question
      await QuestionOptionService.addOptions(data.id, options);
    }
    res.status(200).json({
      status: "success",
      data,
    });
  } catch (error) {
    console.error(error);
    next(error);
  }
};

exports.getById = async (req, res, next) => {
  try {
    const data = await QuestionService.findOne({
      where: {
        id: req.params.id,
      },
    });

    res.status(200).send({
      status: "success",
      data,
    });
  } catch (error) {
    next(error);
  }
};

exports.update = async (req, res, next) => {
  try {
    const [affectedRows] = await QuestionService.update(req.body, {
      where: {
        id: req.params.id,
      },
    });

    res.status(200).json({
      status: "success",
      data: {
        affectedRows,
      },
    });
  } catch (error) {
    next(error);
  }
};

exports.delete = async (req, res, next) => {
  try {
    const affectedRows = await QuestionService.delete({
      where: {
        id: req.params.id,
      },
    });

    res.status(200).send({
      status: "success",
      data: {
        affectedRows,
      },
    });
  } catch (error) {
    next(error);
  }
};
