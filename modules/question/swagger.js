/**
 * @swagger
 * components:
 *   schemas:
 *     question:
 *       type: object
 *       required:
 *         - .required()
         - .required()
 *       properties:
 *         id:
           type: string
           description: The id of the question
         defaultValue:
           type: string
           description: The defaultValue of the question
         allowNull:
           type: string
           description: The allowNull of the question
         primaryKey:
           type: string
           description: The primaryKey of the question
         :
           type: string
           description: The  of the question
 *       example:
 *         id: "Example id"
         defaultValue: "Example defaultValue"
         allowNull: "Example allowNull"
         primaryKey: "Example primaryKey"
         : "Example "
 *
 *     CreatequestionRequest:
 *       type: object
 *       required:
 *         - .required()
         - .required()
 *       properties:
 *         question:
           type: string
         questionType:
           type: string
         explanation:
           type: string
         isActive:
           type: string
         quizId:
           type: string
 *       example:
 *         question: "Example question"
         questionType: "Example questionType"
         explanation: "Example explanation"
         isActive: "Example isActive"
         quizId: "Example quizId"
 *
 *     UpdatequestionRequest:
 *       type: object
 *       properties:
 *         question:
           type: string
         questionType:
           type: string
         explanation:
           type: string
         isActive:
           type: string
         quizId:
           type: string
 *       example:
 *         question: "Example question"
         questionType: "Example questionType"
         explanation: "Example explanation"
         isActive: "Example isActive"
         quizId: "Example quizId"
 *
 *   parameters:
 *     questionId:
 *       in: path
 *       name: id
 *       required: true
 *       schema:
 *         type: string
 *         format: uuid
 *       description: The question ID
 *
 *   responses:
 *     UnauthorizedError:
 *       description: Access token is missing or invalid
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: integer
 *                 example: 401
 *               message:
 *                 type: string
 *                 example: Unauthorized attempt, login again!
 *
 * paths:
 *   /api/v1/question:
 *     get:
 *       summary: Get all questions
 *       tags: [questions]
 *       parameters:
 *         - in: query
 *           name: page
 *           schema:
 *             type: integer
 *             default: 1
 *           description: Page number for pagination
 *         - in: query
 *           name: limit
 *           schema:
 *             type: integer
 *             default: 10
 *           description: Number of items per page
 *       responses:
 *         200:
 *           description: List of questions
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: questions retrieved successfully
 *                   data:
 *                     type: object
 *                     properties:
 *                       questions:
 *                         type: array
 *                         items:
 *                           $ref: '#/components/schemas/question'
 *                       pagination:
 *                         type: object
 *                         properties:
 *                           total:
 *                             type: integer
 *                             description: Total number of questions
 *                           page:
 *                             type: integer
 *                             description: Current page number
 *                           limit:
 *                             type: integer
 *                             description: Number of items per page
 *                           pages:
 *                             type: integer
 *                             description: Total number of pages
 *
 *   /api/v1/question/{id}:
 *     get:
 *       summary: Get question by ID
 *       tags: [questions]
 *       parameters:
 *         - $ref: '#/components/parameters/questionId'
 *       responses:
 *         200:
 *           description: question retrieved successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: question retrieved successfully
 *                   data:
 *                     $ref: '#/components/schemas/question'
 *         404:
 *           description: question not found
 *
 *   /api/v1/admin/question:
 *     post:
 *       summary: Create a new question
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/CreatequestionRequest'
 *       responses:
 *         201:
 *           description: question created successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 201
 *                   message:
 *                     type: string
 *                     example: question created successfully
 *                   data:
 *                     $ref: '#/components/schemas/question'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *
 *   /api/v1/admin/question/{id}:
 *     patch:
 *       summary: Update a question
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/questionId'
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/UpdatequestionRequest'
 *       responses:
 *         200:
 *           description: question updated successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: question updated successfully
 *                   data:
 *                     $ref: '#/components/schemas/question'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: question not found
 *
 *     delete:
 *       summary: Delete a question
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/questionId'
 *       responses:
 *         200:
 *           description: question deleted successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: question deleted successfully
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: question not found
 */