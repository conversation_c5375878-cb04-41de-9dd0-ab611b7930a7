/**
 * @swagger
 * components:
 *   schemas:
 *     notification:
 *       type: object
 *       required:
 *         
 *       properties:
 *         id:
           type: string
           description: The id of the notification
         defaultValue:
           type: string
           description: The defaultValue of the notification
         allowNull:
           type: string
           description: The allowNull of the notification
         primaryKey:
           type: string
           description: The primaryKey of the notification
 *       example:
 *         id: "Example id"
         defaultValue: "Example defaultValue"
         allowNull: "Example allowNull"
         primaryKey: "Example primaryKey"
 *
 *     CreatenotificationRequest:
 *       type: object
 *       required:
 *         
 *       properties:
 *         title:
           type: string
         body:
           type: string
         topic:
           type: string
         click_action:
           type: string
         AdminId:
           type: string
         schedule:
           type: string
         :
           type: string
 *       example:
 *         title: "Example title"
         body: "Example body"
         topic: "Example topic"
         click_action: "Example click_action"
         AdminId: "Example AdminId"
         schedule: "Example schedule"
         : "Example "
 *
 *     UpdatenotificationRequest:
 *       type: object
 *       properties:
 *         title:
           type: string
         body:
           type: string
         topic:
           type: string
         click_action:
           type: string
         AdminId:
           type: string
         schedule:
           type: string
         :
           type: string
 *       example:
 *         title: "Example title"
         body: "Example body"
         topic: "Example topic"
         click_action: "Example click_action"
         AdminId: "Example AdminId"
         schedule: "Example schedule"
         : "Example "
 *
 *   parameters:
 *     notificationId:
 *       in: path
 *       name: id
 *       required: true
 *       schema:
 *         type: string
 *         format: uuid
 *       description: The notification ID
 *
 *   responses:
 *     UnauthorizedError:
 *       description: Access token is missing or invalid
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: integer
 *                 example: 401
 *               message:
 *                 type: string
 *                 example: Unauthorized attempt, login again!
 *
 * paths:
 *   /api/v1/notification:
 *     get:
 *       summary: Get all notifications
 *       tags: [notifications]
 *       parameters:
 *         - in: query
 *           name: page
 *           schema:
 *             type: integer
 *             default: 1
 *           description: Page number for pagination
 *         - in: query
 *           name: limit
 *           schema:
 *             type: integer
 *             default: 10
 *           description: Number of items per page
 *       responses:
 *         200:
 *           description: List of notifications
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: notifications retrieved successfully
 *                   data:
 *                     type: object
 *                     properties:
 *                       notifications:
 *                         type: array
 *                         items:
 *                           $ref: '#/components/schemas/notification'
 *                       pagination:
 *                         type: object
 *                         properties:
 *                           total:
 *                             type: integer
 *                             description: Total number of notifications
 *                           page:
 *                             type: integer
 *                             description: Current page number
 *                           limit:
 *                             type: integer
 *                             description: Number of items per page
 *                           pages:
 *                             type: integer
 *                             description: Total number of pages
 *
 *   /api/v1/notification/{id}:
 *     get:
 *       summary: Get notification by ID
 *       tags: [notifications]
 *       parameters:
 *         - $ref: '#/components/parameters/notificationId'
 *       responses:
 *         200:
 *           description: notification retrieved successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: notification retrieved successfully
 *                   data:
 *                     $ref: '#/components/schemas/notification'
 *         404:
 *           description: notification not found
 *
 *   /api/v1/admin/notification:
 *     post:
 *       summary: Create a new notification
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/CreatenotificationRequest'
 *       responses:
 *         201:
 *           description: notification created successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 201
 *                   message:
 *                     type: string
 *                     example: notification created successfully
 *                   data:
 *                     $ref: '#/components/schemas/notification'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *
 *   /api/v1/admin/notification/{id}:
 *     patch:
 *       summary: Update a notification
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/notificationId'
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/UpdatenotificationRequest'
 *       responses:
 *         200:
 *           description: notification updated successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: notification updated successfully
 *                   data:
 *                     $ref: '#/components/schemas/notification'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: notification not found
 *
 *     delete:
 *       summary: Delete a notification
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/notificationId'
 *       responses:
 *         200:
 *           description: notification deleted successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: notification deleted successfully
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: notification not found
 */