"use strict";

const router = require("express").Router();
const apiLogs = require("./controller");

// Get all API logs with pagination, filtering, and search
router.route("/").get(apiLogs.getAllLogs);

// Clear all API logs - this must come before the /:id routes
router.route("/clear").delete(apiLogs.clearAllLogs);

// Get analytics - this must come before the /:id routes
router.route("/analytics").get(apiLogs.getAnalytics);

// Get a single API log by ID
router.route("/:id").get(apiLogs.getLogById);

// Delete an API log by ID
router.route("/:id").delete(apiLogs.deleteLog);

module.exports = router;
