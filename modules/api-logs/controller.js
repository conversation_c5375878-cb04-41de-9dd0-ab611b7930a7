const ApiLog = require("../../models/ApiLog");

/**
 * Get all API logs with pagination, filtering, and search
 */
exports.getAllLogs = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      sort = "timestamp",
      sortBy = "DESC",
      search = "",
      startDate,
      endDate,
      method,
      path,
      statusCode,
      userId,
      userEmail,
      ipAddress,
    } = req.query;

    // Build filter object
    const filter = {};

    // Add date filter if provided
    if (startDate || endDate) {
      filter.timestamp = {};
      if (startDate) filter.timestamp.$gte = new Date(startDate);
      if (endDate) filter.timestamp.$lte = new Date(endDate);
    }

    // Add other filters if provided
    if (method) filter.method = method;
    if (path) filter.path = { $regex: path, $options: "i" };
    if (statusCode) filter.statusCode = parseInt(statusCode);
    if (userId) filter.userId = userId;
    if (userEmail) filter.userEmail = { $regex: userEmail, $options: "i" };
    if (ipAddress) filter.ipAddress = { $regex: ipAddress, $options: "i" };

    // Add search functionality
    if (search) {
      filter.$or = [
        { path: { $regex: search, $options: "i" } },
        { userEmail: { $regex: search, $options: "i" } },
        { ipAddress: { $regex: search, $options: "i" } },
      ];
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Determine sort direction
    const sortDirection = sortBy.toUpperCase() === "DESC" ? -1 : 1;

    // Execute query
    const logs = await ApiLog.find(filter)
      .sort({ [sort]: sortDirection })
      .skip(skip)
      .limit(parseInt(limit));

    // Get total count for pagination
    const total = await ApiLog.countDocuments(filter);

    res.json({
      data: { rows: logs, count: total },
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / parseInt(limit)),
      },
    });
  } catch (error) {
    console.error("Error fetching API logs:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

/**
 * Get a single API log by ID
 */
exports.getLogById = async (req, res) => {
  try {
    const log = await ApiLog.findById(req.params.id);

    if (!log) {
      return res.status(404).json({ error: "API log not found" });
    }

    res.json({ data: log });
  } catch (error) {
    console.error("Error fetching API log:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

/**
 * Delete an API log by ID
 */
exports.deleteLog = async (req, res) => {
  try {
    const log = await ApiLog.findByIdAndDelete(req.params.id);

    if (!log) {
      return res.status(404).json({ error: "API log not found" });
    }

    res.json({ message: "API log deleted successfully" });
  } catch (error) {
    console.error("Error deleting API log:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

/**
 * Clear all API logs
 */
exports.clearAllLogs = async (req, res) => {
  try {
    await ApiLog.deleteMany({});
    res.json({ message: "All API logs cleared successfully" });
  } catch (error) {
    console.error("Error clearing API logs:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

exports.getAnalytics = async (req, res) => {
  try {
    // Get date range from query params or use default (last 30 days)
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 30); // Default to last 30 days

    // Build the match condition for the aggregation
    const matchCondition = {
      timestamp: {
        $gte: startDate,
        $lte: endDate,
      },
    };

    // If date range is provided in query params, override the default
    if (req.query.startDate && req.query.endDate) {
      matchCondition.timestamp = {
        $gte: new Date(req.query.startDate),
        $lte: new Date(req.query.endDate),
      };
    }

    // Perform MongoDB aggregation to get analytics data
    const [
      statusCodeStats,
      methodStats,
      durationStats,
      timeSeriesStats,
      overallStats,
    ] = await Promise.all([
      // Status code distribution
      ApiLog.aggregate([
        { $match: matchCondition },
        { $group: { _id: "$statusCode", count: { $sum: 1 } } },
        { $project: { status: { $toString: "$_id" }, count: 1, _id: 0 } },
      ]),

      // HTTP method distribution
      ApiLog.aggregate([
        { $match: matchCondition },
        { $group: { _id: "$method", count: { $sum: 1 } } },
        { $project: { method: "$_id", count: 1, _id: 0 } },
      ]),

      // Top 10 slowest endpoints
      ApiLog.aggregate([
        { $match: matchCondition },
        { $group: { _id: "$path", avgDuration: { $avg: "$duration" } } },
        { $sort: { avgDuration: -1 } },
        { $limit: 10 },
        {
          $project: {
            path: "$_id",
            duration: { $round: ["$avgDuration", 2] },
            _id: 0,
          },
        },
      ]),

      // Request volume over time
      ApiLog.aggregate([
        { $match: matchCondition },
        {
          $group: {
            _id: {
              year: { $year: "$timestamp" },
              month: { $month: "$timestamp" },
              day: { $dayOfMonth: "$timestamp" },
            },
            count: { $sum: 1 },
          },
        },
        {
          $project: {
            date: {
              $concat: [
                { $toString: "$_id.month" },
                "/",
                { $toString: "$_id.day" },
              ],
            },
            count: 1,
            _id: 0,
          },
        },
        { $sort: { "_id.year": 1, "_id.month": 1, "_id.day": 1 } },
      ]),

      // Overall statistics
      ApiLog.aggregate([
        { $match: matchCondition },
        {
          $group: {
            _id: null,
            totalRequests: { $sum: 1 },
            avgDuration: { $avg: "$duration" },
            successCount: {
              $sum: {
                $cond: [
                  {
                    $and: [
                      { $gte: ["$statusCode", 200] },
                      { $lt: ["$statusCode", 400] },
                    ],
                  },
                  1,
                  0,
                ],
              },
            },
            errorCount: {
              $sum: {
                $cond: [{ $gte: ["$statusCode", 400] }, 1, 0],
              },
            },
          },
        },
        {
          $project: {
            _id: 0,
            totalRequests: 1,
            avgDuration: { $round: ["$avgDuration", 2] },
            successRate: {
              $multiply: [
                { $divide: ["$successCount", "$totalRequests"] },
                100,
              ],
            },
            errorRate: {
              $multiply: [{ $divide: ["$errorCount", "$totalRequests"] }, 100],
            },
          },
        },
      ]),
    ]);

    // Format the response
    const response = {
      statusCodeData: statusCodeStats,
      methodData: methodStats,
      durationData: durationStats,
      timeSeriesData: timeSeriesStats,
      stats: overallStats[0] || {
        totalRequests: 0,
        avgDuration: 0,
        successRate: 0,
        errorRate: 0,
      },
    };

    res.status(200).json({
      success: true,
      data: response,
    });
  } catch (error) {
    console.error("Error fetching analytics:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching analytics data",
      error: error.message,
    });
  }
};
