"use strict";

const router = require("express").Router();
const userCourse = require("./controller");
const joiSchema = require("./joiSchema");
const { joiValidator } = require("../../middlewares/joiValidator");
// const { authMiddleware, protectRoute } = require("../../middlewares/auth");


router
  .route("/")
  .get(userCourse.getAll)
  .post(joiValidator(joiSchema.create), userCourse.add);
router
  .route("/:id")
  .get(userCourse.getById)
  .patch(joiValidator(joiSchema.update), userCourse.update)
  .delete(userCourse.delete);

module.exports = router;
