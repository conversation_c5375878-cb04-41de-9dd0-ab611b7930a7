"use strict";
const { DataTypes } = require("sequelize");
const { v4: uuidv4 } = require("uuid");
const sequelize = require("../../config/db");
const User = require("../user/model");
const Course = require("../course/model");

const UserCourse = sequelize.define(
  "userGroup",
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: uuidv4,
      allowNull: false,
      primaryKey: true,
    },
    progress: {
      type: DataTypes.FLOAT,
      defaultValue: 0,
    },
  },
  {
    paranoid: true,
  }
);

// Relationships
User.hasMany(UserCourse, { foreignKey: { allowNull: false } });
UserCourse.belongsTo(User);

Course.hasMany(UserCourse, { foreignKey: { allowNull: false } });
UserCourse.belongsTo(Course);

module.exports = UserCourse;
