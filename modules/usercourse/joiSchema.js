const Joi = require("joi");

module.exports = {
  create: Joi.object().keys({
    progress: Joi.number().required(),
    userId: Joi.string().guid({ version: "uuidv4" }).required(),
    courseId: Joi.string().guid({ version: "uuidv4" }).required(),
  }),
  update: Joi.object().keys({
    progress: Joi.number(),
    userId: Joi.string().guid({ version: "uuidv4" }),
    courseId: Joi.string().guid({ version: "uuidv4" }),
  }),
};
