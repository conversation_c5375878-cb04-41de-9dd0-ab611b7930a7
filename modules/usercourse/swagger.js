/**
 * @swagger
 * components:
 *   schemas:
 *     usercourse:
 *       type: object
 *       required:
 *         - .required()
 *       properties:
 *         id:
           type: string
           description: The id of the usercourse
         defaultValue:
           type: string
           description: The defaultValue of the usercourse
         allowNull:
           type: string
           description: The allowNull of the usercourse
         primaryKey:
           type: string
           description: The primaryKey of the usercourse
         :
           type: string
           description: The  of the usercourse
 *       example:
 *         id: "Example id"
         defaultValue: "Example defaultValue"
         allowNull: "Example allowNull"
         primaryKey: "Example primaryKey"
         : "Example "
 *
 *     CreateusercourseRequest:
 *       type: object
 *       required:
 *         - .required()
 *       properties:
 *         progress:
           type: string
         userId:
           type: string
 *       example:
 *         progress: "Example progress"
         userId: "Example userId"
 *
 *     UpdateusercourseRequest:
 *       type: object
 *       properties:
 *         progress:
           type: string
         userId:
           type: string
 *       example:
 *         progress: "Example progress"
         userId: "Example userId"
 *
 *   parameters:
 *     usercourseId:
 *       in: path
 *       name: id
 *       required: true
 *       schema:
 *         type: string
 *         format: uuid
 *       description: The usercourse ID
 *
 *   responses:
 *     UnauthorizedError:
 *       description: Access token is missing or invalid
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: integer
 *                 example: 401
 *               message:
 *                 type: string
 *                 example: Unauthorized attempt, login again!
 *
 * paths:
 *   /api/v1/usercourse:
 *     get:
 *       summary: Get all usercourses
 *       tags: [usercourses]
 *       parameters:
 *         - in: query
 *           name: page
 *           schema:
 *             type: integer
 *             default: 1
 *           description: Page number for pagination
 *         - in: query
 *           name: limit
 *           schema:
 *             type: integer
 *             default: 10
 *           description: Number of items per page
 *       responses:
 *         200:
 *           description: List of usercourses
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: usercourses retrieved successfully
 *                   data:
 *                     type: object
 *                     properties:
 *                       usercourses:
 *                         type: array
 *                         items:
 *                           $ref: '#/components/schemas/usercourse'
 *                       pagination:
 *                         type: object
 *                         properties:
 *                           total:
 *                             type: integer
 *                             description: Total number of usercourses
 *                           page:
 *                             type: integer
 *                             description: Current page number
 *                           limit:
 *                             type: integer
 *                             description: Number of items per page
 *                           pages:
 *                             type: integer
 *                             description: Total number of pages
 *
 *   /api/v1/usercourse/{id}:
 *     get:
 *       summary: Get usercourse by ID
 *       tags: [usercourses]
 *       parameters:
 *         - $ref: '#/components/parameters/usercourseId'
 *       responses:
 *         200:
 *           description: usercourse retrieved successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: usercourse retrieved successfully
 *                   data:
 *                     $ref: '#/components/schemas/usercourse'
 *         404:
 *           description: usercourse not found
 *
 *   /api/v1/admin/usercourse:
 *     post:
 *       summary: Create a new usercourse
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/CreateusercourseRequest'
 *       responses:
 *         201:
 *           description: usercourse created successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 201
 *                   message:
 *                     type: string
 *                     example: usercourse created successfully
 *                   data:
 *                     $ref: '#/components/schemas/usercourse'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *
 *   /api/v1/admin/usercourse/{id}:
 *     patch:
 *       summary: Update a usercourse
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/usercourseId'
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/UpdateusercourseRequest'
 *       responses:
 *         200:
 *           description: usercourse updated successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: usercourse updated successfully
 *                   data:
 *                     $ref: '#/components/schemas/usercourse'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: usercourse not found
 *
 *     delete:
 *       summary: Delete a usercourse
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/usercourseId'
 *       responses:
 *         200:
 *           description: usercourse deleted successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: usercourse deleted successfully
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: usercourse not found
 */