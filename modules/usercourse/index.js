"use strict";

const router = require("express").Router();
const userCourse = require("./controller");
// const { authMiddleware, protectRoute } = require("../../middlewares/auth");
// const { joiValidator } = require("../../middlewares/joiValidator");

router
  .route("/")
  .get(userCourse.getAll)

//   .post(joiValidator(joiSchema.create), userCourse.add);
router
  .route("/:id")
  .get(userCourse.getById)
//   .patch(joiValidator(joiSchema.update), userCourse.update)
//   .delete(userCourse.delete);

module.exports = router;
