"use strict";
const UserCourseService = require("./service");
const { sqquery } = require("../../utils/query");

exports.getAll = async (req, res, next) => {
  try {
    const data = await UserCourseService.findAndCountAll(sqquery(req.query));

    res.status(200).send({
      status: "success",
      data,
    });
  } catch (error) {
    next(error);
  }
};
exports.add = async (req, res, next) => {
  try {
    const data = await UserCourseService.create(req.body);

    res.status(200).json({
      status: "success",
      data,
    });
  } catch (error) {
    // console.error(error);
    next(error);
  }
};

exports.getById = async (req, res, next) => {
  try {
    const data = await UserCourseService.findOne({
      where: {
        id: req.params.id,
      },
    });

    res.status(200).send({
      status: "success",
      data,
    });
  } catch (error) {
    next(error);
  }
};

exports.update = async (req, res, next) => {
  try {
    const [affectedRows] = await UserCourseService.update(req.body, {
      where: {
        id: req.params.id,
      },
    });

    res.status(200).json({
      status: "success",
      data: {
        affectedRows,
      },
    });
  } catch (error) {
    next(error);
  }
};

exports.delete = async (req, res, next) => {
  try {
    const affectedRows = await UserCourseService.delete({
      where: {
        id: req.params.id,
      },
    });

    res.status(200).send({
      status: "success",
      data: {
        affectedRows,
      },
    });
  } catch (error) {
    next(error);
  }
};
