/**
 * @swagger
 * components:
 *   schemas:
 *     proposecard:
 *       type: object
 *       required:
 *         - .required()
         - .required()
         - .required()
         - .required()
         - .required()
 *       properties:
 *         id:
           type: string
           description: The id of the proposecard
         defaultValue:
           type: string
           description: The defaultValue of the proposecard
         allowNull:
           type: string
           description: The allowNull of the proposecard
         primaryKey:
           type: string
           description: The primaryKey of the proposecard
         :
           type: string
           description: The  of the proposecard
 *       example:
 *         id: "Example id"
         defaultValue: "Example defaultValue"
         allowNull: "Example allowNull"
         primaryKey: "Example primaryKey"
         : "Example "
 *
 *     CreateproposecardRequest:
 *       type: object
 *       required:
 *         - .required()
         - .required()
         - .required()
         - .required()
         - .required()
 *       properties:
 *         word:
           type: string
         example:
           type: string
         meaning:
           type: string
         pronunciation:
           type: string
         image:
           type: string
         audio:
           type: string
         status:
           type: string
         languageId:
           type: string
 *       example:
 *         word: "Example word"
         example: "Example example"
         meaning: "Example meaning"
         pronunciation: "Example pronunciation"
         image: "Example image"
         audio: "Example audio"
         status: "Example status"
         languageId: "Example languageId"
 *
 *     UpdateproposecardRequest:
 *       type: object
 *       properties:
 *         word:
           type: string
         example:
           type: string
         meaning:
           type: string
         pronunciation:
           type: string
         image:
           type: string
         audio:
           type: string
         status:
           type: string
         languageId:
           type: string
 *       example:
 *         word: "Example word"
         example: "Example example"
         meaning: "Example meaning"
         pronunciation: "Example pronunciation"
         image: "Example image"
         audio: "Example audio"
         status: "Example status"
         languageId: "Example languageId"
 *
 *   parameters:
 *     proposecardId:
 *       in: path
 *       name: id
 *       required: true
 *       schema:
 *         type: string
 *         format: uuid
 *       description: The proposecard ID
 *
 *   responses:
 *     UnauthorizedError:
 *       description: Access token is missing or invalid
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: integer
 *                 example: 401
 *               message:
 *                 type: string
 *                 example: Unauthorized attempt, login again!
 *
 * paths:
 *   /api/v1/proposecard:
 *     get:
 *       summary: Get all proposecards
 *       tags: [proposecards]
 *       parameters:
 *         - in: query
 *           name: page
 *           schema:
 *             type: integer
 *             default: 1
 *           description: Page number for pagination
 *         - in: query
 *           name: limit
 *           schema:
 *             type: integer
 *             default: 10
 *           description: Number of items per page
 *       responses:
 *         200:
 *           description: List of proposecards
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: proposecards retrieved successfully
 *                   data:
 *                     type: object
 *                     properties:
 *                       proposecards:
 *                         type: array
 *                         items:
 *                           $ref: '#/components/schemas/proposecard'
 *                       pagination:
 *                         type: object
 *                         properties:
 *                           total:
 *                             type: integer
 *                             description: Total number of proposecards
 *                           page:
 *                             type: integer
 *                             description: Current page number
 *                           limit:
 *                             type: integer
 *                             description: Number of items per page
 *                           pages:
 *                             type: integer
 *                             description: Total number of pages
 *
 *   /api/v1/proposecard/{id}:
 *     get:
 *       summary: Get proposecard by ID
 *       tags: [proposecards]
 *       parameters:
 *         - $ref: '#/components/parameters/proposecardId'
 *       responses:
 *         200:
 *           description: proposecard retrieved successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: proposecard retrieved successfully
 *                   data:
 *                     $ref: '#/components/schemas/proposecard'
 *         404:
 *           description: proposecard not found
 *
 *   /api/v1/admin/proposecard:
 *     post:
 *       summary: Create a new proposecard
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/CreateproposecardRequest'
 *       responses:
 *         201:
 *           description: proposecard created successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 201
 *                   message:
 *                     type: string
 *                     example: proposecard created successfully
 *                   data:
 *                     $ref: '#/components/schemas/proposecard'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *
 *   /api/v1/admin/proposecard/{id}:
 *     patch:
 *       summary: Update a proposecard
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/proposecardId'
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/UpdateproposecardRequest'
 *       responses:
 *         200:
 *           description: proposecard updated successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: proposecard updated successfully
 *                   data:
 *                     $ref: '#/components/schemas/proposecard'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: proposecard not found
 *
 *     delete:
 *       summary: Delete a proposecard
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/proposecardId'
 *       responses:
 *         200:
 *           description: proposecard deleted successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: proposecard deleted successfully
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: proposecard not found
 */