"use strict";

const router = require("express").Router();
const proposeCard = require("./controller");
// const { authMiddleware, protectRoute } = require("../../middlewares/auth");
// const { joiValidator } = require("../../middlewares/joiValidator");

router
  .route("/")
  .get(proposeCard.getAll)

//   .post(joiValidator(joiSchema.create), proposeCard.add);
router
  .route("/:id")
  .get(proposeCard.getById)
//   .patch(joiValidator(joiSchema.update), proposeCard.update)
//   .delete(proposeCard.delete);

module.exports = router;
