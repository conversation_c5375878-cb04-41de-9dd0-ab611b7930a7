const Joi = require("joi");

module.exports = {
  create: Joi.object().keys({
    word: Joi.string().required(),
    example: Joi.string().required(),
    meaning: Joi.string().required(),
    pronunciation: Joi.string().allow(null),
    image: Joi.string().required(),
    audio: Joi.string().required(),
    status: Joi.string(),
    languageId: Joi.string().guid({ version: "uuidv4" }).required(),
  }),
  update: Joi.object().keys({
    word: Joi.string(),
    example: Joi.string(),
    meaning: Joi.string(),
    pronunciation: Joi.string(),
    image: Joi.string(),
    audio: Joi.string(),
    status: Joi.string(),
    languageId: Joi.string().guid({ version: "uuidv4" }),
  }),
};
