"use strict";
const { DataTypes } = require("sequelize");
const { v4: uuidv4 } = require("uuid");
const sequelize = require("../../config/db");
const User = require("../user/model");
const Language = require("../language/model");

const ProposeCard = sequelize.define(
  "proposecard",
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: uuidv4,
      allowNull: false,
      primaryKey: true,
    },
    word: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    pronunciation: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    meaning: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    example: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    image: {
      type: DataTypes.STRING,
      allowNull: true,
    }, 
    audio: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    status: {
      type: DataTypes.ENUM("pending", "approved", "rejected"),
      defaultValue: "pending",
    },
  },
  {
    paranoid: true,
  }
);

// Relationships
User.hasMany(ProposeCard, { foreignKey: { allowNull: false } });
ProposeCard.belongsTo(User);
Language.hasMany(ProposeCard, { foreignKey: { allowNull: false } });
ProposeCard.belongsTo(Language);
User.hasMany(ProposeCard, { as: "ReviewedCards", foreignKey: "reviewerId" });
ProposeCard.belongsTo(User, { as: "Reviewer", foreignKey: "reviewerId" });

module.exports = ProposeCard;
