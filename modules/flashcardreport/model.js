"use strict";
const { DataTypes } = require("sequelize");
const { v4: uuidv4 } = require("uuid");
const sequelize = require("../../config/db");
const User = require("../user/model");
const Flashcard = require("../flashcard/model");

const FlashcardReport = sequelize.define("flashcardReport", {
  id: {
    type: DataTypes.UUID,
    defaultValue: uuidv4,
    allowNull: false,
    primaryKey: true,
  },
  title: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  reason: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  status: {
    type: DataTypes.STRING,
    defaultValue: "Pending",
  },
}, {
  paranoid: true,
});

// Relationships
User.hasMany(FlashcardReport, { foreignKey: { allowNull: false } });
FlashcardReport.belongsTo(User);

Flashcard.hasMany(FlashcardReport, { foreignKey: { allowNull: false } });
FlashcardReport.belongsTo(Flashcard);

module.exports = FlashcardReport;
