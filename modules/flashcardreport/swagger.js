/**
 * @swagger
 * components:
 *   schemas:
 *     flashcardreport:
 *       type: object
 *       required:
 *         
 *       properties:
 *         id:
           type: string
           description: The id of the flashcardreport
         defaultValue:
           type: string
           description: The defaultValue of the flashcardreport
         allowNull:
           type: string
           description: The allowNull of the flashcardreport
         primaryKey:
           type: string
           description: The primaryKey of the flashcardreport
         :
           type: string
           description: The  of the flashcardreport
 *       example:
 *         id: "Example id"
         defaultValue: "Example defaultValue"
         allowNull: "Example allowNull"
         primaryKey: "Example primaryKey"
         : "Example "
 *
 *     CreateflashcardreportRequest:
 *       type: object
 *       required:
 *         
 *       properties:
 *         // userId:
           type: string
 *       example:
 *         // userId: "Example // userId"
 *
 *     UpdateflashcardreportRequest:
 *       type: object
 *       properties:
 *         // userId:
           type: string
 *       example:
 *         // userId: "Example // userId"
 *
 *   parameters:
 *     flashcardreportId:
 *       in: path
 *       name: id
 *       required: true
 *       schema:
 *         type: string
 *         format: uuid
 *       description: The flashcardreport ID
 *
 *   responses:
 *     UnauthorizedError:
 *       description: Access token is missing or invalid
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: integer
 *                 example: 401
 *               message:
 *                 type: string
 *                 example: Unauthorized attempt, login again!
 *
 * paths:
 *   /api/v1/flashcardreport:
 *     get:
 *       summary: Get all flashcardreports
 *       tags: [flashcardreports]
 *       parameters:
 *         - in: query
 *           name: page
 *           schema:
 *             type: integer
 *             default: 1
 *           description: Page number for pagination
 *         - in: query
 *           name: limit
 *           schema:
 *             type: integer
 *             default: 10
 *           description: Number of items per page
 *       responses:
 *         200:
 *           description: List of flashcardreports
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: flashcardreports retrieved successfully
 *                   data:
 *                     type: object
 *                     properties:
 *                       flashcardreports:
 *                         type: array
 *                         items:
 *                           $ref: '#/components/schemas/flashcardreport'
 *                       pagination:
 *                         type: object
 *                         properties:
 *                           total:
 *                             type: integer
 *                             description: Total number of flashcardreports
 *                           page:
 *                             type: integer
 *                             description: Current page number
 *                           limit:
 *                             type: integer
 *                             description: Number of items per page
 *                           pages:
 *                             type: integer
 *                             description: Total number of pages
 *
 *   /api/v1/flashcardreport/{id}:
 *     get:
 *       summary: Get flashcardreport by ID
 *       tags: [flashcardreports]
 *       parameters:
 *         - $ref: '#/components/parameters/flashcardreportId'
 *       responses:
 *         200:
 *           description: flashcardreport retrieved successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: flashcardreport retrieved successfully
 *                   data:
 *                     $ref: '#/components/schemas/flashcardreport'
 *         404:
 *           description: flashcardreport not found
 *
 *   /api/v1/admin/flashcardreport:
 *     post:
 *       summary: Create a new flashcardreport
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/CreateflashcardreportRequest'
 *       responses:
 *         201:
 *           description: flashcardreport created successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 201
 *                   message:
 *                     type: string
 *                     example: flashcardreport created successfully
 *                   data:
 *                     $ref: '#/components/schemas/flashcardreport'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *
 *   /api/v1/admin/flashcardreport/{id}:
 *     patch:
 *       summary: Update a flashcardreport
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/flashcardreportId'
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/UpdateflashcardreportRequest'
 *       responses:
 *         200:
 *           description: flashcardreport updated successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: flashcardreport updated successfully
 *                   data:
 *                     $ref: '#/components/schemas/flashcardreport'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: flashcardreport not found
 *
 *     delete:
 *       summary: Delete a flashcardreport
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/flashcardreportId'
 *       responses:
 *         200:
 *           description: flashcardreport deleted successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: flashcardreport deleted successfully
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: flashcardreport not found
 */