"use strict";

const router = require("express").Router();
const flashcardReport = require("./controller");
const joiSchema = require("./joiSchema");
const { joiValidator } = require("../../middlewares/joiValidator");
// const { authMiddleware, protectRoute } = require("../../middlewares/auth");


router
  .route("/")
  .get(flashcardReport.getAll)
  .post(joiValidator(joiSchema.create), flashcardReport.add);
router
  .route("/:id")
  .get(flashcardReport.getById)
  .patch(joiValidator(joiSchema.update), flashcardReport.update)
  .delete(flashcardReport.delete);

module.exports = router;
