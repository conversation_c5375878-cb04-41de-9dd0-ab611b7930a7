"use strict";

const router = require("express").Router();
const flashcardReport = require("./controller");
// const { authMiddleware, protectRoute } = require("../../middlewares/auth");
// const { joiValidator } = require("../../middlewares/joiValidator");

router
  .route("/")
  .get(flashcardReport.getAll)

//   .post(joiValidator(joiSchema.create), flashcardReport.add);
router
  .route("/:id")
  .get(flashcardReport.getById)
//   .patch(joiValidator(joiSchema.update), flashcardReport.update)
//   .delete(flashcardReport.delete);

module.exports = router;
