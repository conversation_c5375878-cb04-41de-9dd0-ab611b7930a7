"use strict";

const router = require("express").Router();
const language = require("./controller");
const joiSchema = require("./joiSchema");
const { joiValidator } = require("../../middlewares/joiValidator");
// const { authMiddleware, protectRoute } = require("../../middlewares/auth");


router
  .route("/")
  .get(language.getAll)
  .post(joiValidator(joiSchema.create), language.add);
router
  .route("/:id")
  .get(language.getById)
  .patch(joiValidator(joiSchema.update), language.update)
  .delete(language.delete);

module.exports = router;
