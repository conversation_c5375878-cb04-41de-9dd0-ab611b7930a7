"use strict";

const router = require("express").Router();
const language = require("./controller");
// const { authMiddleware, protectRoute } = require("../../middlewares/auth");
// const { joiValidator } = require("../../middlewares/joiValidator");

router
  .route("/")
  .get(language.getAll)
// router.route("/base").get(language.baselanguageCreator)
//   .post(joiValidator(joiSchema.create), language.add);
router
  .route("/:id")
  .get(language.getById)
//   .patch(joiValidator(joiSchema.update), language.update)
//   .delete(language.delete);

module.exports = router;
