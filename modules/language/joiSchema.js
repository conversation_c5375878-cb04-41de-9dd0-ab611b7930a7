const Joi = require("joi");

module.exports = {
  create: Joi.object().keys({
    name: Joi.string().required(),
    code: Joi.string().required(),
    englishName: Joi.string().required(),
    isActive: Joi.boolean(),
    isPremium: Joi.boolean(),
    pronunciation: Joi.string().allow(null),
    flag: Joi.string().allow(null),
  }),
  update: Joi.object().keys({
    name: Joi.string(),
    code: Joi.string(),
    isActive: Joi.boolean(),
    isPremium: Joi.boolean(),
    englishName: Joi.string(),
    pronunciation: Joi.string(),
    flag: Joi.string(),
  }),
};
