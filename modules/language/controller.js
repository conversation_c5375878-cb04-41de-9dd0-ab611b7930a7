"use strict";
const LanguageService = require("./service");
const { sqquery } = require("../../utils/query");
const { ROLES } = require("../../middlewares/auth");
// const langugeJSON = require("../../constants/languages.json")
exports.getAll = async (req, res, next) => {
  try {
    const condition = {}
    if(req?.requestor?.role === ROLES.USER){
      condition.isActive = true
    }
    const data = await LanguageService.findAndCountAll(sqquery(req.query,condition,["name","code","englishName","pronunciation"]));

    res.status(200).send({
      status: "success",
      data,
    });
  } catch (error) {
    next(error);
  }
};
exports.add = async (req, res, next) => {
  try {
    const data = await LanguageService.create(req.body);

    res.status(200).json({
      status: "success",
      data,
    });
  } catch (error) {
    // console.error(error);
    next(error);
  }
};

exports.getById = async (req, res, next) => {
  try {
    const data = await LanguageService.findOne({
      where: {
        id: req.params.id,
      },
    });

    res.status(200).send({
      status: "success",
      data,
    });
  } catch (error) {
    next(error);
  }
};

exports.update = async (req, res, next) => {
  try {
    const [affectedRows] = await LanguageService.update(req.body, {
      where: {
        id: req.params.id,
      },
    });

    res.status(200).json({
      status: "success",
      data: {
        affectedRows,
      },
    });
  } catch (error) {
    next(error);
  }
};

exports.delete = async (req, res, next) => {
  try {
    const affectedRows = await LanguageService.delete({
      where: {
        id: req.params.id,
      },
    });

    res.status(200).send({
      status: "success",
      data: {
        affectedRows,
      },
    });
  } catch (error) {
    next(error);
  }
};

exports.baselanguageCreator = async (req, res, next) => {
  try {
    const affectedRows = await LanguageService.bulkCreate(require("../../constants/languages.json"));
    res.status(200).send({
      status: "success",
      data: {
        affectedRows,
      },
    });
  } catch (error) {
    next(error);
  }
};
