/**
 * @swagger
 * components:
 *   schemas:
 *     language:
 *       type: object
 *       required:
 *         - .required()
         - .required()
         - .required()
 *       properties:
 *         id:
           type: string
           description: The id of the language
         defaultValue:
           type: string
           description: The defaultValue of the language
         allowNull:
           type: string
           description: The allowNull of the language
         primaryKey:
           type: string
           description: The primaryKey of the language
 *       example:
 *         id: "Example id"
         defaultValue: "Example defaultValue"
         allowNull: "Example allowNull"
         primaryKey: "Example primaryKey"
 *
 *     CreatelanguageRequest:
 *       type: object
 *       required:
 *         - .required()
         - .required()
         - .required()
 *       properties:
 *         name:
           type: string
         code:
           type: string
         englishName:
           type: string
         isActive:
           type: string
         isPremium:
           type: string
         pronunciation:
           type: string
         flag:
           type: string
         :
           type: string
 *       example:
 *         name: "Example name"
         code: "Example code"
         englishName: "Example englishName"
         isActive: "Example isActive"
         isPremium: "Example isPremium"
         pronunciation: "Example pronunciation"
         flag: "Example flag"
         : "Example "
 *
 *     UpdatelanguageRequest:
 *       type: object
 *       properties:
 *         name:
           type: string
         code:
           type: string
         englishName:
           type: string
         isActive:
           type: string
         isPremium:
           type: string
         pronunciation:
           type: string
         flag:
           type: string
         :
           type: string
 *       example:
 *         name: "Example name"
         code: "Example code"
         englishName: "Example englishName"
         isActive: "Example isActive"
         isPremium: "Example isPremium"
         pronunciation: "Example pronunciation"
         flag: "Example flag"
         : "Example "
 *
 *   parameters:
 *     languageId:
 *       in: path
 *       name: id
 *       required: true
 *       schema:
 *         type: string
 *         format: uuid
 *       description: The language ID
 *
 *   responses:
 *     UnauthorizedError:
 *       description: Access token is missing or invalid
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: integer
 *                 example: 401
 *               message:
 *                 type: string
 *                 example: Unauthorized attempt, login again!
 *
 * paths:
 *   /api/v1/language:
 *     get:
 *       summary: Get all languages
 *       tags: [languages]
 *       parameters:
 *         - in: query
 *           name: page
 *           schema:
 *             type: integer
 *             default: 1
 *           description: Page number for pagination
 *         - in: query
 *           name: limit
 *           schema:
 *             type: integer
 *             default: 10
 *           description: Number of items per page
 *       responses:
 *         200:
 *           description: List of languages
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: languages retrieved successfully
 *                   data:
 *                     type: object
 *                     properties:
 *                       languages:
 *                         type: array
 *                         items:
 *                           $ref: '#/components/schemas/language'
 *                       pagination:
 *                         type: object
 *                         properties:
 *                           total:
 *                             type: integer
 *                             description: Total number of languages
 *                           page:
 *                             type: integer
 *                             description: Current page number
 *                           limit:
 *                             type: integer
 *                             description: Number of items per page
 *                           pages:
 *                             type: integer
 *                             description: Total number of pages
 *
 *   /api/v1/language/{id}:
 *     get:
 *       summary: Get language by ID
 *       tags: [languages]
 *       parameters:
 *         - $ref: '#/components/parameters/languageId'
 *       responses:
 *         200:
 *           description: language retrieved successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: language retrieved successfully
 *                   data:
 *                     $ref: '#/components/schemas/language'
 *         404:
 *           description: language not found
 *
 *   /api/v1/admin/language:
 *     post:
 *       summary: Create a new language
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/CreatelanguageRequest'
 *       responses:
 *         201:
 *           description: language created successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 201
 *                   message:
 *                     type: string
 *                     example: language created successfully
 *                   data:
 *                     $ref: '#/components/schemas/language'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *
 *   /api/v1/admin/language/{id}:
 *     patch:
 *       summary: Update a language
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/languageId'
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/UpdatelanguageRequest'
 *       responses:
 *         200:
 *           description: language updated successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: language updated successfully
 *                   data:
 *                     $ref: '#/components/schemas/language'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: language not found
 *
 *     delete:
 *       summary: Delete a language
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/languageId'
 *       responses:
 *         200:
 *           description: language deleted successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: language deleted successfully
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: language not found
 */