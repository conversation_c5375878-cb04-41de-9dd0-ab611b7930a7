"use strict";

const router = require("express").Router();
const flashcard = require("./controller");
// const { authMiddleware, protectRoute } = require("../../middlewares/auth");
// const { joiValidator } = require("../../middlewares/joiValidator");

router
  .route("/")
  .get(flashcard.getAll)

//   .post(joiValidator(joiSchema.create), flashcard.add);
router
  .route("/:id")
  .get(flashcard.getById)
router.route("/likedislike/:id").get(flashcard.likedislike)
router.route("/bookmark/:id").get(flashcard.toggleBookmark)

//   .patch(joiValidator(joiSchema.update), flashcard.update)
//   .delete(flashcard.delete);

module.exports = router;
