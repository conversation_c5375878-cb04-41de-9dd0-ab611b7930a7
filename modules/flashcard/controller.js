"use strict";
const sequelize = require("../../config/db");
const path = require("path");
const FlashCardService = require("./service");
const { sqquery } = require("../../utils/query");
const SimilarWordModel = require("../similarWord/model");
const FlashcardLike = require("../flashcardlike/model");
const FlashcardBookmark = require("../flashcardbookmark/model");
const FlashcardDislike = require("../flashcarddislike/model");
const Language = require("../language/model");
const SimilarWord = require("../similarWord/model");
const UserLanguage = require("../userlanguage/model");
const {
  getFilePath,
  getOriginalFileName,
} = require("../../middlewares/multer/helper");
const { convertTextToAudio } = require("./utility");

exports.getAll = async (req, res, next) => {
  try {
    const userPreferredLanguages = await UserLanguage.findAll({
      where: {
        userId: req.requestor.id,
      },
      include: [
        {
          model: Language,
          attributes: ["id"], // Assuming Language has an 'id' field
        },
      ],
    });
    const languageIds = userPreferredLanguages.map((upl) => upl.id);
    const queryOption = {
      ...sqquery(req.query),
      attributes: [
        "id",
        "word",
        "pronunciation",
        "meaning",
        "isActive",
        "createdAt",
        "languageId",
        "example",
        "image",
        "audio",
      ],
      include: [
        {
          model: Language,
          required: true,
        },
        {
          model: SimilarWord,
          required: false,
        },
        {
          model: FlashcardLike,
          required: false,
          where: {
            userId: req.requestor.id,
          },
        },
        {
          model: FlashcardBookmark,
          required: false,
          where: {
            userId: req.requestor.id,
          },
        },
        {
          model: FlashcardDislike,
          required: false,
          where: {
            userId: req.requestor.id,
          },
        },
      ],
    };
    if (languageIds.length > 0) {
      queryOption.where = {
        languageId: {
          [sequelize.Op.in]: languageIds,
        },
      };
    }
    const data = await FlashCardService.findAndCountAll(queryOption);

    res.status(200).send({
      status: "success",
      data,
    });
  } catch (error) {
    next(error);
  }
};

exports.getAllforAdmin = async (req, res, next) => {
  try {
    const data = await FlashCardService.findAndCountAll({
      ...sqquery(req.query, {}, ["word", "pronunciation", "meaning"]),
      attributes: [
        "id",
        "word",
        "pronunciation",
        "meaning",
        "isActive",
        "createdAt",
        "languageId",
        "example",
        "image",
        "audio",
        [
          sequelize.literal(
            `(SELECT COUNT(*) FROM "${process.env.DEVSCHEMA}"."flashcardLikes" WHERE "flashcardLikes"."flashcardId" = "flashcard"."id")`
          ),
          "likes",
        ],
        [
          sequelize.literal(
            `(SELECT COUNT(*) FROM "${process.env.DEVSCHEMA}"."flashcardDislikes" WHERE "flashcardDislikes"."flashcardId" = "flashcard"."id")`
          ),
          "dislikes",
        ],
        [
          sequelize.literal(
            `(SELECT COUNT(*) FROM "${process.env.DEVSCHEMA}"."flashcardBookmarks" WHERE "flashcardBookmarks"."flashcardId" = "flashcard"."id")`
          ),
          "bookmarks",
        ],
        [
          sequelize.literal(
            `(SELECT COUNT(*) FROM "${process.env.DEVSCHEMA}"."courseFlashcards" WHERE "flashcard"."id" = "courseFlashcards"."flashcardId")`
          ),
          "coursesCount",
        ],
        [
          sequelize.literal(
            `(SELECT COUNT(*) FROM "${process.env.DEVSCHEMA}"."groupFlashcards" WHERE "flashcard"."id" = "groupFlashcards"."flashcardId")`
          ),
          "groupsCount",
        ],
      ],
      include: [
        {
          model: Language,
          required: true,
          attributes: ["code", "name", "englishName"],
        },
      ],
    });

    res.status(200).send({
      status: "success",
      data,
    });
  } catch (error) {
    console.error("Error in getAllforAdmin:", error);
    next(error);
  }
};

exports.add = async (req, res, next) => {
  try {
    // console.log(req?.files , req?.body )
    const flashCardImagePath = req?.files?.image
      ? getFilePath(req.files.image[0])
      : null;

    const flashCardAudioPath = req?.files?.audio
      ? getFilePath(req.files.audio[0])
      : null;

    if (flashCardImagePath) {
      req.body.image = flashCardImagePath;
    }

    if (flashCardAudioPath) {
      req.body.audio = flashCardAudioPath;
    }

    const { similarWords, ...bodyData } = req.body;

    const data = await FlashCardService.create(bodyData);
    if (similarWords && similarWords.length > 0) {
      const similarWordsData = similarWords.map((word) => ({
        word: word,
        flashcardId: data.id,
      }));
      await SimilarWordModel.bulkCreate(similarWordsData);
    }
    res.status(200).json({
      status: "success",
      data,
    });
  } catch (error) {
    // console.error(error);
    next(error);
  }
};

exports.getById = async (req, res, next) => {
  try {
    const data = await FlashCardService.findOne({
      where: {
        id: req.params.id,
      },
      attributes: [
        "id",
        "word",
        "pronunciation",
        "meaning",
        "isActive",
        "createdAt",
        "languageId",
        "example",
        "image",
        "audio",
        [
          sequelize.literal(
            `(SELECT COUNT(*) FROM "${process.env.DEVSCHEMA}"."flashcardLikes" WHERE "flashcardLikes"."flashcardId" = "flashcard"."id")`
          ),
          "likes",
        ],
        [
          sequelize.literal(
            `(SELECT COUNT(*) FROM "${process.env.DEVSCHEMA}"."flashcardDislikes" WHERE "flashcardDislikes"."flashcardId" = "flashcard"."id")`
          ),
          "dislikes",
        ],
        [
          sequelize.literal(
            `(SELECT COUNT(*) FROM "${process.env.DEVSCHEMA}"."flashcardBookmarks" WHERE "flashcardBookmarks"."flashcardId" = "flashcard"."id")`
          ),
          "bookmarks",
        ],
        [
          sequelize.literal(
            `(SELECT COUNT(*) FROM "${process.env.DEVSCHEMA}"."courseFlashcards" WHERE "flashcard"."id" = "courseFlashcards"."flashcardId")`
          ),
          "coursesCount",
        ],
        [
          sequelize.literal(
            `(SELECT COUNT(*) FROM "${process.env.DEVSCHEMA}"."groupFlashcards" WHERE "flashcard"."id" = "groupFlashcards"."flashcardId")`
          ),
          "groupsCount",
        ],
      ],
      include: [
        {
          model: Language,
          required: true,
          attributes: ["code", "name", "englishName"],
        },
      ],
    });

    res.status(200).send({
      status: "success",
      data,
    });
  } catch (error) {
    next(error);
  }
};

exports.update = async (req, res, next) => {
  try {
    const flashCardImagePath = req?.files?.image
      ? getFilePath(req.files.image[0])
      : null;

    const flashCardAudioPath = req?.files?.audio
      ? getFilePath(req.files.audio[0])
      : null;

    const flashCardImageOriginalName = req?.files?.image
      ? getOriginalFileName(req.files.image[0])
      : null;

    const flashCardAudioOriginalName = req?.files?.audio
      ? getOriginalFileName(req.files.audio[0])
      : null;
    if (flashCardImagePath) {
      req.body.image = flashCardImagePath;
      console.log(
        req?.files?.image,
        "req?.files?.image",
        flashCardImageOriginalName,
        ",,,,",
        flashCardAudioOriginalName
      );
    }

    if (flashCardAudioPath) {
      req.body.audio = flashCardAudioPath;
    }

    const [affectedRows] = await FlashCardService.update(req.body, {
      where: {
        id: req.params.id,
      },
    });

    res.status(200).json({
      status: "success",
      data: {
        affectedRows,
      },
    });
  } catch (error) {
    next(error);
  }
};

exports.delete = async (req, res, next) => {
  try {
    const affectedRows = await FlashCardService.delete({
      where: {
        id: req.params.id,
      },
    });

    res.status(200).send({
      status: "success",
      data: {
        affectedRows,
      },
    });
  } catch (error) {
    next(error);
  }
};

exports.likedislike = async (req, res, next) => {
  try {
    const { id } = req.params; // Flashcard ID
    const userId = req.requestor.id; // Assuming the user ID is available from the authenticated user

    // Check if the user has already liked the flashcard
    const existingLike = await FlashcardLike.findOne({
      where: {
        flashcardId: id,
        userId,
      },
    });

    // Check if the user has already disliked the flashcard
    const existingDislike = await FlashcardDislike.findOne({
      where: {
        flashcardId: id,
        userId,
      },
    });

    let affectedRows = 0;

    if (existingLike) {
      // User has liked the flashcard, so convert it to a dislike
      await existingLike.destroy(); // Remove the like
      await FlashcardDislike.create({ flashcardId: id, userId }); // Add a dislike
      affectedRows = 1;
    } else if (existingDislike) {
      // User has disliked the flashcard, so convert it to a like
      await existingDislike.destroy(); // Remove the dislike
      await FlashcardLike.create({ flashcardId: id, userId }); // Add a like
      affectedRows = 1;
    } else {
      // User has neither liked nor disliked, so we can assume a new like
      await FlashcardLike.create({ flashcardId: id, userId });
      affectedRows = 1;
    }

    res.status(200).send({
      status: "success",
      data: {
        affectedRows,
      },
    });
  } catch (error) {
    next(error);
  }
};

exports.toggleBookmark = async (req, res, next) => {
  try {
    const { id } = req.params; // Flashcard ID
    const userId = req.requestor.id; // Assuming the user ID is available from the authenticated user

    // Check if the user has already bookmarked the flashcard
    const existingBookmark = await FlashcardBookmark.findOne({
      where: {
        flashcardId: id,
        userId,
      },
    });

    let affectedRows = 0;

    if (existingBookmark) {
      // User has bookmarked the flashcard, so remove the bookmark
      await existingBookmark.destroy();
      affectedRows = 1;
    } else {
      // User has not bookmarked the flashcard, so add a bookmark
      await FlashcardBookmark.create({ flashcardId: id, userId });
      affectedRows = 1;
    }

    res.status(200).send({
      status: "success",
      data: {
        affectedRows,
      },
    });
  } catch (error) {
    next(error);
  }
};
function parseFilename(filename) {
  // Remove the file extension
  const nameWithoutExt = path.parse(filename).name;

  // Split by underscore
  const [word, pronunciation, meaning, example] = nameWithoutExt.split("_");

  // Return parsed data if at least the required fields are present
  if (word) {
    return { word, pronunciation, meaning, example };
  }

  return null;
}
exports.bulkUploadViaImage = async (req, res, next) => {
  try {
    const { image } = req?.files;
    const { languageId } = req?.body;

    if (!image || image.length === 0) {
      return res.status(400).json({ error: "No images uploaded" });
    }
    const language = await Language.findByPk(languageId, {
      attributes: ["code"],
    });
    const languageCode = language ? language?.code : null;
    if (!languageCode) {
      return res.status(404).json({ error: "Language not found" });
    }
    let successCount = 0;
    let failureCount = 0;

    const creationPromises = image?.map(async (file) => {
      try {
        const flashCardImagePath = getFilePath(file);
        const flashCardImageOriginalName = getOriginalFileName(file);
        const { filename } = file;

        const parsedData = parseFilename(flashCardImageOriginalName);

        if (parsedData) {
          const { word, pronunciation, meaning, example } = parsedData;
          const audio = await convertTextToAudio(word, languageCode);
          // Create the Flashcard
          await FlashCardService.create({
            word,
            languageId,
            pronunciation: pronunciation || null,
            meaning: meaning || null,
            example: example || null,
            image: flashCardImagePath,
            audio,
          });

          successCount++;
        } else {
          console.error(
            `Filename ${filename} does not match the required format.`
          );
          failureCount++;
        }
      } catch (error) {
        console.error(`Error processing file ${file.originalname}:`, error);
        failureCount++;
      }
    });

    await Promise.all(creationPromises);

    return res.status(201).json({
      message: `Bulk image upload complete success : ${successCount}`,
      successCount,
      failureCount,
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({ error: "Bulk image upload failed" });
  }
};
