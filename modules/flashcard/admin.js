"use strict";

const router = require("express").Router();
const flashcard = require("./controller");
const joiSchema = require("./joiSchema");
const { joiValidator } = require("../../middlewares/joiValidator");
const { upload } = require("../../middlewares/multer");
const { setUploadFolder } = require("../../middlewares/multer/helper");
// const { authMiddleware, protectRoute } = require("../../middlewares/auth");

router
  .route("/")
  .get(flashcard.getAllforAdmin)
  .post(
    setUploadFolder("flashcard"),
    upload.fields([
      { name: "image", maxCount: 1 },
      { name: "audio", maxCount: 1 },
    ]),
    joiValidator(joiSchema.create),
    flashcard.add
  );

router
  .route("/bulk-image")
  .post(
    setUploadFolder("flashcard"),
    upload.fields([{ name: "image", maxCount: 50 }]),
    joiValidator(joiSchema.bulkImage),
    flashcard.bulkUploadViaImage
  );
  router.route("/likedislike/:id").get(flashcard.likedislike)
router.route("/bookmark/:id").get(flashcard.toggleBookmark)
// .post(upload.single("image"),joiValidator(joiSchema.create), flashcard.add);
router
  .route("/:id")
  .get(flashcard.getById)
  .patch(
    setUploadFolder("flashcard"),
    upload.fields([
      { name: "image", maxCount: 1 },
      { name: "audio", maxCount: 1 },
    ]),
    joiValidator(joiSchema.update),
    flashcard.update
  )
  .delete(flashcard.delete);

module.exports = router;
