const { uploadFilePathBased } = require('../../middlewares/multer');
const path = require('path');
const fs = require('fs');
const axios = require('axios');

async function fetchAudioFromGoogleTranslateAndSave(word, languageCode = 'en') {
    const encodedWord = encodeURIComponent(word);
    console.log('encodedWord', encodedWord);
    const outputLocationPath = path.resolve(
        __dirname,
        `${Date.now()}_output.mp3`
    );
    const url = `https://translate.google.com.vn/translate_tts?ie=UTF-8&q=${encodedWord}&tl=${languageCode}&client=tw-ob`;
    console.log('url', url);
    const writer = fs.createWriteStream(outputLocationPath);

    try {
        const response = await axios({
            method: 'get',
            url: url,
            responseType: 'stream',
        });

        response.data.pipe(writer);
        return new Promise((resolve, reject) => {
            writer.on('finish', () => resolve(outputLocationPath));
            writer.on('error', reject);
        });
    } catch (error) {
        console.error('Error fetching from: ', error);
        throw error;
    }
}

exports.convertTextToAudio = async (text, languageCode) => {
    try {
        const localFilePath = await fetchAudioFromGoogleTranslateAndSave(text, languageCode);
        const s3Url = await uploadFilePathBased(localFilePath, 'flashcard/audio');
        console.log('s3URL', s3Url);
        fs.unlinkSync(localFilePath);
        return s3Url;
    } catch (error) {
        console.error('Error converting text to audio:', error);
        throw error;
    }
};
