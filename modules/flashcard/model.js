"use strict";
const { DataTypes } = require("sequelize");
const { v4: uuidv4 } = require('uuid');
const sequelize = require("../../config/db");
const Language = require("../language/model");
// const Level = require("../level/model");
const ProposeCard = require("../proposecard/model");
const SimilarWord = require("../similarWord/model");

const Flashcard = sequelize.define(
  "flashcard",
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: uuidv4,
      allowNull: false,
      primaryKey: true,
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    word: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    pronunciation: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    meaning: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    example: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    image: {
      type: DataTypes.TEXT,
      allowNull: true,
    }, 
    audio: {
      type: DataTypes.STRING,
      allowNull: true,
    },
  },
  {
    paranoid: true,
  }
);
// Flashcard.sync({alter : true});
Language.hasMany(Flashcard, { foreignKey: { allowNull: false } });
Flashcard.belongsTo(Language);

ProposeCard.hasOne(Flashcard, {foreignKey: { allowNull: true }});
Flashcard.belongsTo(ProposeCard, {foreignKey: { allowNull: true }});

Flashcard.hasMany(SimilarWord, { foreignKey: { allowNull: false }});
SimilarWord.belongsTo(Flashcard);

module.exports = Flashcard;
