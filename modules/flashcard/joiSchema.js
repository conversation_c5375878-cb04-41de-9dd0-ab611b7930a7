const Joi = require("joi");

module.exports = {
  create: Joi.object().keys({
    word: Joi.string().required(),
    example: Joi.string().required(),
    meaning: Joi.string().required(),
    pronunciation: Joi.string().allow(null),
    image: Joi.any(),
    audio: Joi.any(),
    isActive: Joi.boolean(),
    languageId: Joi.string().guid({ version: "uuidv4" }).required(),
    similarWords: Joi.array().items(Joi.string()).optional(),
  }),
  update: Joi.object().keys({
    word: Joi.string(),
    example: Joi.string(),
    meaning: Joi.string(),
    pronunciation: Joi.string(),
    image: Joi.any(),
    audio: Joi.any(),
    isActive: Joi.boolean(),
    languageId: Joi.string().guid({ version: "uuidv4" }),
  }),
  bulkImage: Joi.object().keys({
    languageId: Joi.string().guid({ version: "uuidv4" }).required(),
    image: Joi.any(),
  }),
};
