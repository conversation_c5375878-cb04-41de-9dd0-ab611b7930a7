/**
 * @swagger
 * components:
 *   schemas:
 *     flashcard:
 *       type: object
 *       required:
 *         - .required()
         - .required()
         - .required()
 *       properties:
 *         id:
           type: string
           description: The id of the flashcard
         defaultValue:
           type: string
           description: The defaultValue of the flashcard
         allowNull:
           type: string
           description: The allowNull of the flashcard
         primaryKey:
           type: string
           description: The primaryKey of the flashcard
         :
           type: string
           description: The  of the flashcard
 *       example:
 *         id: "Example id"
         defaultValue: "Example defaultValue"
         allowNull: "Example allowNull"
         primaryKey: "Example primaryKey"
         : "Example "
 *
 *     CreateflashcardRequest:
 *       type: object
 *       required:
 *         - .required()
         - .required()
         - .required()
 *       properties:
 *         word:
           type: string
         example:
           type: string
         meaning:
           type: string
         pronunciation:
           type: string
         image:
           type: string
         audio:
           type: string
         isActive:
           type: string
         languageId:
           type: string
 *       example:
 *         word: "Example word"
         example: "Example example"
         meaning: "Example meaning"
         pronunciation: "Example pronunciation"
         image: "Example image"
         audio: "Example audio"
         isActive: "Example isActive"
         languageId: "Example languageId"
 *
 *     UpdateflashcardRequest:
 *       type: object
 *       properties:
 *         word:
           type: string
         example:
           type: string
         meaning:
           type: string
         pronunciation:
           type: string
         image:
           type: string
         audio:
           type: string
         isActive:
           type: string
         languageId:
           type: string
 *       example:
 *         word: "Example word"
         example: "Example example"
         meaning: "Example meaning"
         pronunciation: "Example pronunciation"
         image: "Example image"
         audio: "Example audio"
         isActive: "Example isActive"
         languageId: "Example languageId"
 *
 *   parameters:
 *     flashcardId:
 *       in: path
 *       name: id
 *       required: true
 *       schema:
 *         type: string
 *         format: uuid
 *       description: The flashcard ID
 *
 *   responses:
 *     UnauthorizedError:
 *       description: Access token is missing or invalid
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: integer
 *                 example: 401
 *               message:
 *                 type: string
 *                 example: Unauthorized attempt, login again!
 *
 * paths:
 *   /api/v1/flashcard:
 *     get:
 *       summary: Get all flashcards
 *       tags: [flashcards]
 *       parameters:
 *         - in: query
 *           name: page
 *           schema:
 *             type: integer
 *             default: 1
 *           description: Page number for pagination
 *         - in: query
 *           name: limit
 *           schema:
 *             type: integer
 *             default: 10
 *           description: Number of items per page
 *       responses:
 *         200:
 *           description: List of flashcards
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: flashcards retrieved successfully
 *                   data:
 *                     type: object
 *                     properties:
 *                       flashcards:
 *                         type: array
 *                         items:
 *                           $ref: '#/components/schemas/flashcard'
 *                       pagination:
 *                         type: object
 *                         properties:
 *                           total:
 *                             type: integer
 *                             description: Total number of flashcards
 *                           page:
 *                             type: integer
 *                             description: Current page number
 *                           limit:
 *                             type: integer
 *                             description: Number of items per page
 *                           pages:
 *                             type: integer
 *                             description: Total number of pages
 *
 *   /api/v1/flashcard/{id}:
 *     get:
 *       summary: Get flashcard by ID
 *       tags: [flashcards]
 *       parameters:
 *         - $ref: '#/components/parameters/flashcardId'
 *       responses:
 *         200:
 *           description: flashcard retrieved successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: flashcard retrieved successfully
 *                   data:
 *                     $ref: '#/components/schemas/flashcard'
 *         404:
 *           description: flashcard not found
 *
 *   /api/v1/admin/flashcard:
 *     post:
 *       summary: Create a new flashcard
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/CreateflashcardRequest'
 *       responses:
 *         201:
 *           description: flashcard created successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 201
 *                   message:
 *                     type: string
 *                     example: flashcard created successfully
 *                   data:
 *                     $ref: '#/components/schemas/flashcard'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *
 *   /api/v1/admin/flashcard/{id}:
 *     patch:
 *       summary: Update a flashcard
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/flashcardId'
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/UpdateflashcardRequest'
 *       responses:
 *         200:
 *           description: flashcard updated successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: flashcard updated successfully
 *                   data:
 *                     $ref: '#/components/schemas/flashcard'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: flashcard not found
 *
 *     delete:
 *       summary: Delete a flashcard
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/flashcardId'
 *       responses:
 *         200:
 *           description: flashcard deleted successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: flashcard deleted successfully
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: flashcard not found
 */