"use strict";
const CourseFlashCardService = require("./service");
const { sqquery } = require("../../utils/query");
const Flashcard = require("../flashcard/model");
const createHttpError = require("http-errors");

exports.getAll = async (req, res, next) => {
  try {
    const data = await CourseFlashCardService.findAndCountAll(
      {...sqquery(req.query,), include: [
        {
          model: Flashcard,
          required: true,
          attributes: [
            "id",
            "word",
            "pronunciation",
            "meaning",
            "isActive",
            "createdAt",
            "languageId",
            "example",
            "image",
            "audio",]
        },
      ], }
    );

    res.status(200).send({
      status: "success",
      data,
    });
  } catch (error) {
    next(error);
  }
};
exports.add = async (req, res, next) => {
  try {
    const exist = await CourseFlashCardService.findOne({
      where: {
        courseId: req?.body?.courseId,
        flashcardId: req?.body?.flashcardId,
      },
    });
    if (exist) {
      throw createHttpError(400, `Flashcard already exists in course`);
    }
    const data = await CourseFlashCardService.create(req.body);

    res.status(200).json({
      status: "success",
      data,
    });
  } catch (error) {
    // console.error(error);
    next(error);
  }
};

exports.getById = async (req, res, next) => {
  try {
    const data = await CourseFlashCardService.findOne({
      where: {
        id: req.params.id,
      },
    });

    res.status(200).send({
      status: "success",
      data,
    });
  } catch (error) {
    next(error);
  }
};

exports.update = async (req, res, next) => {
  try {
    const [affectedRows] = await CourseFlashCardService.update(req.body, {
      where: {
        id: req.params.id,
      },
    });

    res.status(200).json({
      status: "success",
      data: {
        affectedRows,
      },
    });
  } catch (error) {
    next(error);
  }
};

exports.delete = async (req, res, next) => {
  try {
    const affectedRows = await CourseFlashCardService.delete({
      where: {
        id: req.params.id,
      },
    });

    res.status(200).send({
      status: "success",
      data: {
        affectedRows,
      },
    });
  } catch (error) {
    next(error);
  }
};
