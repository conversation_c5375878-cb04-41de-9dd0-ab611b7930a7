/**
 * @swagger
 * components:
 *   schemas:
 *     courseflashcard:
 *       type: object
 *       required:
 *         - .required()
         - .required()
 *       properties:
 *         id:
           type: string
           description: The id of the courseflashcard
         defaultValue:
           type: string
           description: The defaultValue of the courseflashcard
         allowNull:
           type: string
           description: The allowNull of the courseflashcard
         primaryKey:
           type: string
           description: The primaryKey of the courseflashcard
         :
           type: string
           description: The  of the courseflashcard
 *       example:
 *         id: "Example id"
         defaultValue: "Example defaultValue"
         allowNull: "Example allowNull"
         primaryKey: "Example primaryKey"
         : "Example "
 *
 *     CreatecourseflashcardRequest:
 *       type: object
 *       required:
 *         - .required()
         - .required()
 *       properties:
 *         position:
           type: string
         // status:
           type: string
         courseId:
           type: string
 *       example:
 *         position: "Example position"
         // status: "Example // status"
         courseId: "Example courseId"
 *
 *     UpdatecourseflashcardRequest:
 *       type: object
 *       properties:
 *         position:
           type: string
         // status:
           type: string
         courseId:
           type: string
 *       example:
 *         position: "Example position"
         // status: "Example // status"
         courseId: "Example courseId"
 *
 *   parameters:
 *     courseflashcardId:
 *       in: path
 *       name: id
 *       required: true
 *       schema:
 *         type: string
 *         format: uuid
 *       description: The courseflashcard ID
 *
 *   responses:
 *     UnauthorizedError:
 *       description: Access token is missing or invalid
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: integer
 *                 example: 401
 *               message:
 *                 type: string
 *                 example: Unauthorized attempt, login again!
 *
 * paths:
 *   /api/v1/courseflashcard:
 *     get:
 *       summary: Get all courseflashcards
 *       tags: [courseflashcards]
 *       parameters:
 *         - in: query
 *           name: page
 *           schema:
 *             type: integer
 *             default: 1
 *           description: Page number for pagination
 *         - in: query
 *           name: limit
 *           schema:
 *             type: integer
 *             default: 10
 *           description: Number of items per page
 *       responses:
 *         200:
 *           description: List of courseflashcards
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: courseflashcards retrieved successfully
 *                   data:
 *                     type: object
 *                     properties:
 *                       courseflashcards:
 *                         type: array
 *                         items:
 *                           $ref: '#/components/schemas/courseflashcard'
 *                       pagination:
 *                         type: object
 *                         properties:
 *                           total:
 *                             type: integer
 *                             description: Total number of courseflashcards
 *                           page:
 *                             type: integer
 *                             description: Current page number
 *                           limit:
 *                             type: integer
 *                             description: Number of items per page
 *                           pages:
 *                             type: integer
 *                             description: Total number of pages
 *
 *   /api/v1/courseflashcard/{id}:
 *     get:
 *       summary: Get courseflashcard by ID
 *       tags: [courseflashcards]
 *       parameters:
 *         - $ref: '#/components/parameters/courseflashcardId'
 *       responses:
 *         200:
 *           description: courseflashcard retrieved successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: courseflashcard retrieved successfully
 *                   data:
 *                     $ref: '#/components/schemas/courseflashcard'
 *         404:
 *           description: courseflashcard not found
 *
 *   /api/v1/admin/courseflashcard:
 *     post:
 *       summary: Create a new courseflashcard
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/CreatecourseflashcardRequest'
 *       responses:
 *         201:
 *           description: courseflashcard created successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 201
 *                   message:
 *                     type: string
 *                     example: courseflashcard created successfully
 *                   data:
 *                     $ref: '#/components/schemas/courseflashcard'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *
 *   /api/v1/admin/courseflashcard/{id}:
 *     patch:
 *       summary: Update a courseflashcard
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/courseflashcardId'
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/UpdatecourseflashcardRequest'
 *       responses:
 *         200:
 *           description: courseflashcard updated successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: courseflashcard updated successfully
 *                   data:
 *                     $ref: '#/components/schemas/courseflashcard'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: courseflashcard not found
 *
 *     delete:
 *       summary: Delete a courseflashcard
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/courseflashcardId'
 *       responses:
 *         200:
 *           description: courseflashcard deleted successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: courseflashcard deleted successfully
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: courseflashcard not found
 */