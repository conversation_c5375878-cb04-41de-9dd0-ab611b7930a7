"use strict";

const router = require("express").Router();
const courseflashcard = require("./controller");
// const { authMiddleware, protectRoute } = require("../../middlewares/auth");
// const { joiValidator } = require("../../middlewares/joiValidator");

router
  .route("/")
  .get(courseflashcard.getAll)

//   .post(joiValidator(joiSchema.create), courseflashcard.add);
router
  .route("/:id")
  .get(courseflashcard.getById)
//   .patch(joiValidator(joiSchema.update), courseflashcard.update)
//   .delete(courseflashcard.delete);

module.exports = router;
