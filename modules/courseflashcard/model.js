"use strict";
const { DataTypes } = require("sequelize");
const { v4: uuidv4 } = require("uuid");
const sequelize = require("../../config/db");
const Flashcard = require("../flashcard/model");
const Course = require("../course/model");

const CourseFlashcard = sequelize.define(
  "courseFlashcard",
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: uuidv4,
      allowNull: false,
      primaryKey: true,
    },
    position: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    status: {
      type: DataTypes.ENUM("pending", "completed"),
      defaultValue: "pending",
    },
  },
  {
    paranoid: true,
  }
);
// CourseFlashcard.sync({alter :true})
// Relationships
Course.hasMany(CourseFlashcard, { foreignKey: { allowNull: false } });
CourseFlashcard.belongsTo(Course);

Flashcard.hasMany(CourseFlashcard, { foreignKey: { allowNull: false } });
CourseFlashcard.belongsTo(Flashcard);

module.exports = CourseFlashcard;
