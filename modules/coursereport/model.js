"use strict";
const { DataTypes } = require("sequelize");
const { v4: uuidv4 } = require("uuid");
const sequelize = require("../../config/db");
const User = require("../user/model");
const Course = require("../course/model");

const CourseReport = sequelize.define("courseReport", {
  id: {
    type: DataTypes.UUID,
    defaultValue: uuidv4,
    allowNull: false,
    primaryKey: true,
  },
  title: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  reason: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  status: {
    type: DataTypes.STRING,
    defaultValue: "Pending",
  },
}, {
  paranoid: true,
});

// Relationships
User.hasMany(CourseReport, { foreignKey: { allowNull: false } });
CourseReport.belongsTo(User);

Course.hasMany(CourseReport, { foreignKey: { allowNull: false } });
CourseReport.belongsTo(Course);

module.exports = CourseReport;
