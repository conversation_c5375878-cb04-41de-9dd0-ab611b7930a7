/**
 * @swagger
 * components:
 *   schemas:
 *     coursereport:
 *       type: object
 *       required:
 *         
 *       properties:
 *         id:
           type: string
           description: The id of the coursereport
         defaultValue:
           type: string
           description: The defaultValue of the coursereport
         allowNull:
           type: string
           description: The allowNull of the coursereport
         primaryKey:
           type: string
           description: The primaryKey of the coursereport
         :
           type: string
           description: The  of the coursereport
 *       example:
 *         id: "Example id"
         defaultValue: "Example defaultValue"
         allowNull: "Example allowNull"
         primaryKey: "Example primaryKey"
         : "Example "
 *
 *     CreatecoursereportRequest:
 *       type: object
 *       required:
 *         
 *       properties:
 *         // userId:
           type: string
 *       example:
 *         // userId: "Example // userId"
 *
 *     UpdatecoursereportRequest:
 *       type: object
 *       properties:
 *         // userId:
           type: string
 *       example:
 *         // userId: "Example // userId"
 *
 *   parameters:
 *     coursereportId:
 *       in: path
 *       name: id
 *       required: true
 *       schema:
 *         type: string
 *         format: uuid
 *       description: The coursereport ID
 *
 *   responses:
 *     UnauthorizedError:
 *       description: Access token is missing or invalid
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: integer
 *                 example: 401
 *               message:
 *                 type: string
 *                 example: Unauthorized attempt, login again!
 *
 * paths:
 *   /api/v1/coursereport:
 *     get:
 *       summary: Get all coursereports
 *       tags: [coursereports]
 *       parameters:
 *         - in: query
 *           name: page
 *           schema:
 *             type: integer
 *             default: 1
 *           description: Page number for pagination
 *         - in: query
 *           name: limit
 *           schema:
 *             type: integer
 *             default: 10
 *           description: Number of items per page
 *       responses:
 *         200:
 *           description: List of coursereports
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: coursereports retrieved successfully
 *                   data:
 *                     type: object
 *                     properties:
 *                       coursereports:
 *                         type: array
 *                         items:
 *                           $ref: '#/components/schemas/coursereport'
 *                       pagination:
 *                         type: object
 *                         properties:
 *                           total:
 *                             type: integer
 *                             description: Total number of coursereports
 *                           page:
 *                             type: integer
 *                             description: Current page number
 *                           limit:
 *                             type: integer
 *                             description: Number of items per page
 *                           pages:
 *                             type: integer
 *                             description: Total number of pages
 *
 *   /api/v1/coursereport/{id}:
 *     get:
 *       summary: Get coursereport by ID
 *       tags: [coursereports]
 *       parameters:
 *         - $ref: '#/components/parameters/coursereportId'
 *       responses:
 *         200:
 *           description: coursereport retrieved successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: coursereport retrieved successfully
 *                   data:
 *                     $ref: '#/components/schemas/coursereport'
 *         404:
 *           description: coursereport not found
 *
 *   /api/v1/admin/coursereport:
 *     post:
 *       summary: Create a new coursereport
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/CreatecoursereportRequest'
 *       responses:
 *         201:
 *           description: coursereport created successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 201
 *                   message:
 *                     type: string
 *                     example: coursereport created successfully
 *                   data:
 *                     $ref: '#/components/schemas/coursereport'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *
 *   /api/v1/admin/coursereport/{id}:
 *     patch:
 *       summary: Update a coursereport
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/coursereportId'
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/UpdatecoursereportRequest'
 *       responses:
 *         200:
 *           description: coursereport updated successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: coursereport updated successfully
 *                   data:
 *                     $ref: '#/components/schemas/coursereport'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: coursereport not found
 *
 *     delete:
 *       summary: Delete a coursereport
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/coursereportId'
 *       responses:
 *         200:
 *           description: coursereport deleted successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: coursereport deleted successfully
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: coursereport not found
 */