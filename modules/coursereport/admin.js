"use strict";

const router = require("express").Router();
const courseReport = require("./controller");
const joiSchema = require("./joiSchema");
const { joiValidator } = require("../../middlewares/joiValidator");
// const { authMiddleware, protectRoute } = require("../../middlewares/auth");


router
  .route("/")
  .get(courseReport.getAll)
  .post(joiValidator(joiSchema.create), courseReport.add);
router
  .route("/:id")
  .get(courseReport.getById)
  .patch(joiValidator(joiSchema.update), courseReport.update)
  .delete(courseReport.delete);

module.exports = router;
