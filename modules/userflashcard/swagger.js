/**
 * @swagger
 * components:
 *   schemas:
 *     userflashcard:
 *       type: object
 *       required:
 *         - .required()
         - .required()
 *       properties:
 *         id:
           type: string
           description: The id of the userflashcard
         defaultValue:
           type: string
           description: The defaultValue of the userflashcard
         allowNull:
           type: string
           description: The allowNull of the userflashcard
         primaryKey:
           type: string
           description: The primaryKey of the userflashcard
         :
           type: string
           description: The  of the userflashcard
 *       example:
 *         id: "Example id"
         defaultValue: "Example defaultValue"
         allowNull: "Example allowNull"
         primaryKey: "Example primaryKey"
         : "Example "
 *
 *     CreateuserflashcardRequest:
 *       type: object
 *       required:
 *         - .required()
         - .required()
 *       properties:
 *         status:
           type: string
         progress:
           type: string
         userId:
           type: string
 *       example:
 *         status: "Example status"
         progress: "Example progress"
         userId: "Example userId"
 *
 *     UpdateuserflashcardRequest:
 *       type: object
 *       properties:
 *         status:
           type: string
         progress:
           type: string
         userId:
           type: string
 *       example:
 *         status: "Example status"
         progress: "Example progress"
         userId: "Example userId"
 *
 *   parameters:
 *     userflashcardId:
 *       in: path
 *       name: id
 *       required: true
 *       schema:
 *         type: string
 *         format: uuid
 *       description: The userflashcard ID
 *
 *   responses:
 *     UnauthorizedError:
 *       description: Access token is missing or invalid
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: integer
 *                 example: 401
 *               message:
 *                 type: string
 *                 example: Unauthorized attempt, login again!
 *
 * paths:
 *   /api/v1/userflashcard:
 *     get:
 *       summary: Get all userflashcards
 *       tags: [userflashcards]
 *       parameters:
 *         - in: query
 *           name: page
 *           schema:
 *             type: integer
 *             default: 1
 *           description: Page number for pagination
 *         - in: query
 *           name: limit
 *           schema:
 *             type: integer
 *             default: 10
 *           description: Number of items per page
 *       responses:
 *         200:
 *           description: List of userflashcards
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: userflashcards retrieved successfully
 *                   data:
 *                     type: object
 *                     properties:
 *                       userflashcards:
 *                         type: array
 *                         items:
 *                           $ref: '#/components/schemas/userflashcard'
 *                       pagination:
 *                         type: object
 *                         properties:
 *                           total:
 *                             type: integer
 *                             description: Total number of userflashcards
 *                           page:
 *                             type: integer
 *                             description: Current page number
 *                           limit:
 *                             type: integer
 *                             description: Number of items per page
 *                           pages:
 *                             type: integer
 *                             description: Total number of pages
 *
 *   /api/v1/userflashcard/{id}:
 *     get:
 *       summary: Get userflashcard by ID
 *       tags: [userflashcards]
 *       parameters:
 *         - $ref: '#/components/parameters/userflashcardId'
 *       responses:
 *         200:
 *           description: userflashcard retrieved successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: userflashcard retrieved successfully
 *                   data:
 *                     $ref: '#/components/schemas/userflashcard'
 *         404:
 *           description: userflashcard not found
 *
 *   /api/v1/admin/userflashcard:
 *     post:
 *       summary: Create a new userflashcard
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/CreateuserflashcardRequest'
 *       responses:
 *         201:
 *           description: userflashcard created successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 201
 *                   message:
 *                     type: string
 *                     example: userflashcard created successfully
 *                   data:
 *                     $ref: '#/components/schemas/userflashcard'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *
 *   /api/v1/admin/userflashcard/{id}:
 *     patch:
 *       summary: Update a userflashcard
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/userflashcardId'
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/UpdateuserflashcardRequest'
 *       responses:
 *         200:
 *           description: userflashcard updated successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: userflashcard updated successfully
 *                   data:
 *                     $ref: '#/components/schemas/userflashcard'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: userflashcard not found
 *
 *     delete:
 *       summary: Delete a userflashcard
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/userflashcardId'
 *       responses:
 *         200:
 *           description: userflashcard deleted successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: userflashcard deleted successfully
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: userflashcard not found
 */