"use strict";
const { DataTypes } = require("sequelize");
const { v4: uuidv4 } = require("uuid");
const sequelize = require("../../config/db");
const User = require("../user/model");
const Flashcard = require("../flashcard/model");

const UserFlashcard = sequelize.define(
  "userFlashcard",
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: uuidv4,
      allowNull: false,
      primaryKey: true,
    },
    status: {
      type: DataTypes.ENUM("learned", "not learned"),
      defaultValue: "not learned",
    },
    progress: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
  },
  {
    paranoid: true,
  }
);

// Relationships
User.hasMany(UserFlashcard, { foreignKey: { allowNull: false } });
UserFlashcard.belongsTo(User);

Flashcard.hasMany(UserFlashcard, { foreignKey: { allowNull: false } });
UserFlashcard.belongsTo(Flashcard);

module.exports = UserFlashcard;
