const Joi = require("joi");

module.exports = {
  create: Joi.object().keys({
    status: Joi.string().required(),
    progress: Joi.number().required(),
    userId: Joi.string().guid({ version: "uuidv4" }).required(),
    flashcardId: Joi.string().guid({ version: "uuidv4" }).required(),
  }),
  update: Joi.object().keys({
    status: Joi.string(),
    progress: Joi.number(),
    userId: Joi.string().guid({ version: "uuidv4" }),
    flashcardId: Joi.string().guid({ version: "uuidv4" }),
  }),
};
