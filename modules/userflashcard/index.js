"use strict";

const router = require("express").Router();
const userFlashcard = require("./controller");
// const { authMiddleware, protectRoute } = require("../../middlewares/auth");
// const { joiValidator } = require("../../middlewares/joiValidator");

router
  .route("/")
  .get(userFlashcard.getAll)

//   .post(joiValidator(joiSchema.create), userFlashcard.add);
router
  .route("/:id")
  .get(userFlashcard.getById)
//   .patch(joiValidator(joiSchema.update), userFlashcard.update)
//   .delete(userFlashcard.delete);

module.exports = router;
