"use strict";
const AppConfigService = require("./service");

exports.getOne = async (req, res, next) => {
  try {
    const data = await AppConfigService.findOne({
      where: {
        id: 1,
      },
    });

    res.status(200).send({
      status: "success",
      data,
    });
  } catch (error) {
    next(error);
  }
};

//for admin
exports.add = async (req, res, next) => {
  try {
    const temp = await AppConfigService.create(req.body);

    res.status(200).json({
      status: "success",
      data: {
        temp,
      },
    });
  } catch (error) {
    next(error);
  }
};

exports.getAll = async (req, res, next) => {
  try {
    const temps = await AppConfigService.findAndCountAll({});

    res.status(200).send({
      status: "success",
      data: temps,
    });
  } catch (error) {
    next(error);
  }
};

exports.getById = async (req, res, next) => {
  try {
    const data = await AppConfigService.findOne({
      where: {
        id: req.params.id,
      },
    });

    res.status(200).send({
      status: "success",
      data,
    });
  } catch (error) {
    next(error);
  }
};
exports.update = async (req, res, next) => {
  try {
    const [affectedRows] = await AppConfigService.update(req.body, {
      where: {
        id: req.params.id,
      },
    });

    res.status(200).send({
      status: "success",
      data: {
        affectedRows,
      },
    });
  } catch (error) {
    next(error);
  }
};

exports.delete = async (req, res, next) => {
  try {
    const affectedRows = await AppConfigService.delete({
      where: {
        id: req.params.id,
      },
    });

    res.status(200).send({
      status: "success",
      message: "delete app config successfully",
      data: {
        affectedRows,
      },
    });
  } catch (error) {
    next(error);
  }
};
