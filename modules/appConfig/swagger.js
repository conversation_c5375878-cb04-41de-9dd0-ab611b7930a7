/**
 * @swagger
 * components:
 *   schemas:
 *     appConfig:
 *       type: object
 *       required:
 *         
 *       properties:
 *         id:
           type: string
           description: The id of the appConfig
         defaultValue:
           type: string
           description: The defaultValue of the appConfig
         allowNull:
           type: string
           description: The allowNull of the appConfig
         primaryKey:
           type: string
           description: The primaryKey of the appConfig
 *       example:
 *         id: "Example id"
         defaultValue: "Example defaultValue"
         allowNull: "Example allowNull"
         primaryKey: "Example primaryKey"
 *
 *     CreateappConfigRequest:
 *       type: object
 *       required:
 *         
 *       properties:
 *         appInMaintenance:
           type: string
         androidVersionCode:
           type: string
         iosVersionCode:
           type: string
         forceUpdate:
           type: string
         softUpdate:
           type: string
         :
           type: string
 *       example:
 *         appInMaintenance: "Example appInMaintenance"
         androidVersionCode: "Example androidVersionCode"
         iosVersionCode: "Example iosVersionCode"
         forceUpdate: "Example forceUpdate"
         softUpdate: "Example softUpdate"
         : "Example "
 *
 *     UpdateappConfigRequest:
 *       type: object
 *       properties:
 *         appInMaintenance:
           type: string
         androidVersionCode:
           type: string
         iosVersionCode:
           type: string
         forceUpdate:
           type: string
         softUpdate:
           type: string
         :
           type: string
 *       example:
 *         appInMaintenance: "Example appInMaintenance"
         androidVersionCode: "Example androidVersionCode"
         iosVersionCode: "Example iosVersionCode"
         forceUpdate: "Example forceUpdate"
         softUpdate: "Example softUpdate"
         : "Example "
 *
 *   parameters:
 *     appconfigId:
 *       in: path
 *       name: id
 *       required: true
 *       schema:
 *         type: string
 *         format: uuid
 *       description: The appConfig ID
 *
 *   responses:
 *     UnauthorizedError:
 *       description: Access token is missing or invalid
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: integer
 *                 example: 401
 *               message:
 *                 type: string
 *                 example: Unauthorized attempt, login again!
 *
 * paths:
 *   /api/v1/appconfig:
 *     get:
 *       summary: Get all appconfigs
 *       tags: [appConfigs]
 *       parameters:
 *         - in: query
 *           name: page
 *           schema:
 *             type: integer
 *             default: 1
 *           description: Page number for pagination
 *         - in: query
 *           name: limit
 *           schema:
 *             type: integer
 *             default: 10
 *           description: Number of items per page
 *       responses:
 *         200:
 *           description: List of appconfigs
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: appConfigs retrieved successfully
 *                   data:
 *                     type: object
 *                     properties:
 *                       appconfigs:
 *                         type: array
 *                         items:
 *                           $ref: '#/components/schemas/appConfig'
 *                       pagination:
 *                         type: object
 *                         properties:
 *                           total:
 *                             type: integer
 *                             description: Total number of appconfigs
 *                           page:
 *                             type: integer
 *                             description: Current page number
 *                           limit:
 *                             type: integer
 *                             description: Number of items per page
 *                           pages:
 *                             type: integer
 *                             description: Total number of pages
 *
 *   /api/v1/appconfig/{id}:
 *     get:
 *       summary: Get appconfig by ID
 *       tags: [appConfigs]
 *       parameters:
 *         - $ref: '#/components/parameters/appconfigId'
 *       responses:
 *         200:
 *           description: appConfig retrieved successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: appConfig retrieved successfully
 *                   data:
 *                     $ref: '#/components/schemas/appConfig'
 *         404:
 *           description: appConfig not found
 *
 *   /api/v1/admin/appconfig:
 *     post:
 *       summary: Create a new appconfig
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/CreateappConfigRequest'
 *       responses:
 *         201:
 *           description: appConfig created successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 201
 *                   message:
 *                     type: string
 *                     example: appConfig created successfully
 *                   data:
 *                     $ref: '#/components/schemas/appConfig'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *
 *   /api/v1/admin/appconfig/{id}:
 *     patch:
 *       summary: Update a appconfig
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/appconfigId'
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/UpdateappConfigRequest'
 *       responses:
 *         200:
 *           description: appConfig updated successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: appConfig updated successfully
 *                   data:
 *                     $ref: '#/components/schemas/appConfig'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: appConfig not found
 *
 *     delete:
 *       summary: Delete a appconfig
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/appconfigId'
 *       responses:
 *         200:
 *           description: appConfig deleted successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: appConfig deleted successfully
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: appConfig not found
 */