"use strict";

const router = require("express").Router();
const appConfig = require("./controller");

router.route("/").get(appConfig.getOne);
router.route("/health").get((req, res) => {
  res.status(200).json({
    status: "success",
    message: "Oroi Backend API is running",
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || "development",
    version: "1.0.0"
  });
});

module.exports = router;
