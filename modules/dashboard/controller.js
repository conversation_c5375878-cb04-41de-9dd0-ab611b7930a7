const sequelize = require("../../config/db");
const { dateFilter } = require("../../utils/service");
const Course = require("../course/model");
const Flashcard = require("../flashcard/model");
const FlashcardDislike = require("../flashcarddislike/model");
const FlashcardLike = require("../flashcardlike/model");
const Group = require("../group/model");
const Language = require("../language/model");
const ProposeCard = require("../proposecard/model");
const User = require("../user/model");

exports.getCounts = async (req, res) => {
  try {
    // Count Users
    // const totalUsers = await User.count();
    const totalUsers = await User.count();
    const blockedUsers = await User.count({ where: { isBlocked: true } });
    const activeUsers = totalUsers - blockedUsers;
    const premiumUsers = await User.count({ where: { isPremium: true } });
    const inpremiumUsers = totalUsers - premiumUsers;

    // Count Languages
    const totalLanguages = await Language.count();
    const activeLanguages = await Language.count({ where: { isActive: true } });
    const inactiveLanguages = totalLanguages - activeLanguages;

    // Count Courses
    const totalCourses = await Course.count();
    const activeCourses = await Course.count({ where: { isActive: true } });
    const inactiveCourses = totalCourses - activeCourses;

    // Count Groups

    const totalGroups = await Group.count();
    const activeGroups = await Group.count({ where: { isActive: true } });
    const inactiveGroups = totalGroups - activeGroups;

    const totalFlashcards = await Flashcard.count();
    const activeFlashcards = await Flashcard.count({ where: { isActive: true } });
    const inactiveFlashcards = totalFlashcards - activeFlashcards;

    // Count ProposedCard
    const totalProposedCard = await ProposeCard.count();
    const acceptedProposedCard = await ProposeCard.count({ where: { status: 'approved' } });
    const rejectedProposedCard = await ProposeCard.count({ where: { status: 'rejected' } });
    const pendingProposedCard = await ProposeCard.count({ where: { status: 'pending' } });

    res.json({data :{
      totalUsers,
      premiumUsers,
      inpremiumUsers,
      blockedUsers,
      activeUsers,
      totalLanguages,
      activeLanguages,
      inactiveLanguages,
      totalCourses,
      activeCourses,
      inactiveCourses,
      totalGroups,
      inactiveGroups,
      activeGroups,
      totalProposedCard,
      acceptedProposedCard,
      rejectedProposedCard,
      pendingProposedCard,
      totalFlashcards,
      activeFlashcards,
      inactiveFlashcards
    }});
  } catch (error) {
    console.error("Error fetching counts", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
};


exports.getDailyData = async (req, res) => {
  try {
    const dateFilterQuery = dateFilter(req.query, 'createdAt');

    // Group by date and count daily users
    const dailyUserCounts = await User.findAll({
      where: {
        ...dateFilterQuery,
      },
      attributes: [
        [sequelize.fn('DATE', sequelize.col('createdAt')), 'date'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'userCount'],
      ],
      group: ['date'],
      raw: true,
    });

    // Group by date and count daily proposed cards
    const dailyProposedCardCounts = await ProposeCard.findAll({
      where: {
        ...dateFilterQuery,
      },
      attributes: [
        [sequelize.fn('DATE', sequelize.col('createdAt')), 'date'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'proposedCardCount'],
      ],
      group: ['date'],
      raw: true,
    });

    res.json({data:{
      dailyUserCounts,
      dailyProposedCardCounts,
    }});
  } catch (error) {
    console.error("Error fetching daily data", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

exports.getChartData = async (req, res) => {
  try {
    // Language-wise data
    const languages = await Language.findAll();
    const languageWise = await Promise.all(languages.map(async (language) => {
      const [courses, flashcards, proposedCards, groups] = await Promise.all([
        Course.count({ where: { languageId: language.id } }),
        Flashcard.count({ where: { languageId: language.id } }),
        ProposeCard.count({ where: { languageId: language.id } }),
        Group.count({ where: { languageId: language.id } }),
      ]);
      return {
        language: `${language.name} (${language.englishName} - ${language.code})`,
        courses,
        flashcards,
        proposedCards,
        groups
      };
    }));

    // Total counts
    const totalCounts = {
      totalCourses: await Course.count(),
      totalFlashcards: await Flashcard.count(),
      totalProposeCards: await ProposeCard.count(),
      totalGroups: await Group.count()
    };

    res.json({ data: { languageWise, totalCounts } });
  } catch (error) {
    console.error("Error fetching chart data", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

exports.getTopFlashcards = async (req, res) => {
  const limit = parseInt(req.query.limit, 10) || 10;

  try {

    // Fetch top liked flashcards
    const topLiked = await FlashcardLike.findAll({
      attributes: [
        // 'id',
        'flashcardId',
        [sequelize.fn('COUNT', sequelize.col('id')), 'likes']
      ],
      // include: [
      //   {
      //     model: FlashcardLike,
      //     attributes: []
      //   }
      // ],
      group: ['flashcardId'],
      order: [[sequelize.literal('likes'), 'DESC']],
      limit: limit < 100 ? limit : 10,
      // raw: true
    });

    // Fetch top disliked flashcards
    const topDisliked = await FlashcardDislike.findAll({
      attributes: [
        // 'id',
        'flashcardId',
        [sequelize.fn('COUNT', sequelize.col('id')), 'dislikes']
      ],
      // include: [
      //   {
      //     model: FlashcardDislike,
      //     attributes: []
      //   }
      // ],
      group: ['flashcardId'],
      order: [[sequelize.literal('dislikes'), 'DESC']],
      limit: limit < 100 ? limit : 10,
      // raw: true
    });
    // const topDisliked = await Flashcard.findAll({
    //   attributes: [
    //     'id',
    //     'word',
    //     [sequelize.fn('COUNT', sequelize.col('FlashcardDislikes.id')), 'dislikes']
    //   ],
    //   include: [
    //     {
    //       model: FlashcardDislike,
    //       attributes: []
    //     }
    //   ],
    //   group: ['Flashcard.id'],
    //   order: [[sequelize.literal('dislikes'), 'DESC']],
    //   limit: limit < 100 ? limit : 10,
    //   raw: true
    // });

    res.json({ topLiked, topDisliked });
  } catch (error) {
    console.error("Error fetching top flashcards", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
};
