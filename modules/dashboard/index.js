"use strict";

const router = require("express").Router();
const dashboard = require("./controller");
// const { authMiddleware, protectRoute } = require("../../middlewares/auth");
// const { joiValidator } = require("../../middlewares/joiValidator");

router
  .route("/")
  .get(dashboard.getAll)

//   .post(joiValidator(joiSchema.create), dashboard.add);
router
  .route("/:id")
  .get(dashboard.getById)
//   .patch(joiValidator(joiSchema.update), dashboard.update)
//   .delete(dashboard.delete);

module.exports = router;
