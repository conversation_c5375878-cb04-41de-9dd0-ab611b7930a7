/**
 * @swagger
 * components:
 *   schemas:
 *     similarWord:
 *       type: object
 *       required:
 *         - .required()
 *       properties:
 *         id:
           type: string
           description: The id of the similarWord
         defaultValue:
           type: string
           description: The defaultValue of the similarWord
         allowNull:
           type: string
           description: The allowNull of the similarWord
         primaryKey:
           type: string
           description: The primaryKey of the similarWord
 *       example:
 *         id: "Example id"
         defaultValue: "Example defaultValue"
         allowNull: "Example allowNull"
         primaryKey: "Example primaryKey"
 *
 *     CreatesimilarWordRequest:
 *       type: object
 *       required:
 *         - .required()
 *       properties:
 *         word:
           type: string
         isActive:
           type: string
         flashcardId:
           type: string
 *       example:
 *         word: "Example word"
         isActive: "Example isActive"
         flashcardId: "Example flashcardId"
 *
 *     UpdatesimilarWordRequest:
 *       type: object
 *       properties:
 *         word:
           type: string
         isActive:
           type: string
         flashcardId:
           type: string
 *       example:
 *         word: "Example word"
         isActive: "Example isActive"
         flashcardId: "Example flashcardId"
 *
 *   parameters:
 *     similarwordId:
 *       in: path
 *       name: id
 *       required: true
 *       schema:
 *         type: string
 *         format: uuid
 *       description: The similarWord ID
 *
 *   responses:
 *     UnauthorizedError:
 *       description: Access token is missing or invalid
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: integer
 *                 example: 401
 *               message:
 *                 type: string
 *                 example: Unauthorized attempt, login again!
 *
 * paths:
 *   /api/v1/similarword:
 *     get:
 *       summary: Get all similarwords
 *       tags: [similarWords]
 *       parameters:
 *         - in: query
 *           name: page
 *           schema:
 *             type: integer
 *             default: 1
 *           description: Page number for pagination
 *         - in: query
 *           name: limit
 *           schema:
 *             type: integer
 *             default: 10
 *           description: Number of items per page
 *       responses:
 *         200:
 *           description: List of similarwords
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: similarWords retrieved successfully
 *                   data:
 *                     type: object
 *                     properties:
 *                       similarwords:
 *                         type: array
 *                         items:
 *                           $ref: '#/components/schemas/similarWord'
 *                       pagination:
 *                         type: object
 *                         properties:
 *                           total:
 *                             type: integer
 *                             description: Total number of similarwords
 *                           page:
 *                             type: integer
 *                             description: Current page number
 *                           limit:
 *                             type: integer
 *                             description: Number of items per page
 *                           pages:
 *                             type: integer
 *                             description: Total number of pages
 *
 *   /api/v1/similarword/{id}:
 *     get:
 *       summary: Get similarword by ID
 *       tags: [similarWords]
 *       parameters:
 *         - $ref: '#/components/parameters/similarwordId'
 *       responses:
 *         200:
 *           description: similarWord retrieved successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: similarWord retrieved successfully
 *                   data:
 *                     $ref: '#/components/schemas/similarWord'
 *         404:
 *           description: similarWord not found
 *
 *   /api/v1/admin/similarword:
 *     post:
 *       summary: Create a new similarword
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/CreatesimilarWordRequest'
 *       responses:
 *         201:
 *           description: similarWord created successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 201
 *                   message:
 *                     type: string
 *                     example: similarWord created successfully
 *                   data:
 *                     $ref: '#/components/schemas/similarWord'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *
 *   /api/v1/admin/similarword/{id}:
 *     patch:
 *       summary: Update a similarword
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/similarwordId'
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/UpdatesimilarWordRequest'
 *       responses:
 *         200:
 *           description: similarWord updated successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: similarWord updated successfully
 *                   data:
 *                     $ref: '#/components/schemas/similarWord'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: similarWord not found
 *
 *     delete:
 *       summary: Delete a similarword
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/similarwordId'
 *       responses:
 *         200:
 *           description: similarWord deleted successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: similarWord deleted successfully
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: similarWord not found
 */