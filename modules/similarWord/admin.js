"use strict";

const router = require("express").Router();
const similarWord = require("./controller");
const joiSchema = require("./joiSchema");
const { joiValidator } = require("../../middlewares/joiValidator");
// const { authMiddleware, protectRoute } = require("../../middlewares/auth");


router
  .route("/")
  .get(similarWord.getAll)
  .post(joiValidator(joiSchema.create), similarWord.add);
router
  .route("/:id")
  .get(similarWord.getById)
  .patch(joiValidator(joiSchema.update), similarWord.update)
  .delete(similarWord.delete);

module.exports = router;
