"use strict";

const router = require("express").Router();
const quiz = require("./controller");
// const { authMiddleware, protectRoute } = require("../../middlewares/auth");
// const { joiValidator } = require("../../middlewares/joiValidator");

router
  .route("/")
  .get(quiz.getAll)

//   .post(joiValidator(joiSchema.create), quiz.add);
router
  .route("/:id")
  .get(quiz.getById)
//   .patch(joiValidator(joiSchema.update), quiz.update)
//   .delete(quiz.delete);

module.exports = router;
