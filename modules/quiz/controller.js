"use strict";
const QuizService = require("./service");
const QuestionService = require("../question/service");
const QuestionOptionService = require("../questionoption/service");
const { sqquery } = require("../../utils/query");

exports.getAll = async (req, res, next) => {
  try {
    const data = await QuizService.findAndCountAll(sqquery(req.query,{},["name","description"]));

    res.status(200).send({
      status: "success",
      data,
    });
  } catch (error) {
    next(error);
  }
};
exports.add = async (req, res, next) => {
  try {
    const { questions, ...quizInfo } = req.body;
    const data = await QuizService.create(quizInfo);
    if (questions && questions.length > 0) {
      await Promise.all(questions.map(async (questionData) => {
        const { options, ...questionInfo } = questionData;
  
        // Add quizId to the question info
        questionInfo.quizId = data.id;
  
        // Create the question
        const question = await QuestionService.create(questionInfo);
  
        // If options are provided, add them
        if (options && options.length > 0) {
          await QuestionOptionService.addOptions(question.id, options);
        }
      }));
    }
    res.status(200).json({
      status: "success",
      data,
    });
  } catch (error) {
    console.error(error);
    next(error);
  }
};

exports.getById = async (req, res, next) => {
  try {
    const data = await QuizService.findOne({
      where: {
        id: req.params.id,
      },
    });

    res.status(200).send({
      status: "success",
      data,
    });
  } catch (error) {
    next(error);
  }
};

exports.update = async (req, res, next) => {
  try {
    const [affectedRows] = await QuizService.update(req.body, {
      where: {
        id: req.params.id,
      },
    });

    res.status(200).json({
      status: "success",
      data: {
        affectedRows,
      },
    });
  } catch (error) {
    next(error);
  }
};

exports.delete = async (req, res, next) => {
  try {
    const affectedRows = await QuizService.delete({
      where: {
        id: req.params.id,
      },
    });

    res.status(200).send({
      status: "success",
      data: {
        affectedRows,
      },
    });
  } catch (error) {
    next(error);
  }
};
