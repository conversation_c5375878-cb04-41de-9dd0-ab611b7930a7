"use strict";

const router = require("express").Router();
const quiz = require("./controller");
const joiSchema = require("./joiSchema");
const { joiValidator } = require("../../middlewares/joiValidator");
// const { authMiddleware, protectRoute } = require("../../middlewares/auth");


router
  .route("/")
  .get(quiz.getAll)
  .post(joiValidator(joiSchema.create), quiz.add);
router
  .route("/:id")
  .get(quiz.getById)
  .patch(joiValidator(joiSchema.update), quiz.update)
  .delete(quiz.delete);

module.exports = router;
