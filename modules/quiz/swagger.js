/**
 * @swagger
 * components:
 *   schemas:
 *     quiz:
 *       type: object
 *       required:
 *         - .required()
         - .required()
         - .required()
         - .required()
         - .required()
         - .required()
         - .required()
 *       properties:
 *         id:
           type: string
           description: The id of the quiz
         defaultValue:
           type: string
           description: The defaultValue of the quiz
         allowNull:
           type: string
           description: The allowNull of the quiz
         primaryKey:
           type: string
           description: The primaryKey of the quiz
         :
           type: string
           description: The  of the quiz
 *       example:
 *         id: "Example id"
         defaultValue: "Example defaultValue"
         allowNull: "Example allowNull"
         primaryKey: "Example primaryKey"
         : "Example "
 *
 *     CreatequizRequest:
 *       type: object
 *       required:
 *         - .required()
         - .required()
         - .required()
         - .required()
         - .required()
         - .required()
         - .required()
 *       properties:
 *         name:
           type: string
         // nameType:
           type: string
         description:
           type: string
         isActive:
           type: string
         questions:
           type: string
         questionType:
           type: string
         explanation:
           type: string
         options:
           type: string
         optionText:
           type: string
         :
           type: string
 *       example:
 *         name: "Example name"
         // nameType: "Example // nameType"
         description: "Example description"
         isActive: "Example isActive"
         questions: "Example questions"
         questionType: "Example questionType"
         explanation: "Example explanation"
         options: "Example options"
         optionText: "Example optionText"
         : "Example "
 *
 *     UpdatequizRequest:
 *       type: object
 *       properties:
 *         name:
           type: string
         // nameType:
           type: string
         description:
           type: string
         isActive:
           type: string
         questions:
           type: string
         questionType:
           type: string
         explanation:
           type: string
         options:
           type: string
         optionText:
           type: string
         :
           type: string
 *       example:
 *         name: "Example name"
         // nameType: "Example // nameType"
         description: "Example description"
         isActive: "Example isActive"
         questions: "Example questions"
         questionType: "Example questionType"
         explanation: "Example explanation"
         options: "Example options"
         optionText: "Example optionText"
         : "Example "
 *
 *   parameters:
 *     quizId:
 *       in: path
 *       name: id
 *       required: true
 *       schema:
 *         type: string
 *         format: uuid
 *       description: The quiz ID
 *
 *   responses:
 *     UnauthorizedError:
 *       description: Access token is missing or invalid
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: integer
 *                 example: 401
 *               message:
 *                 type: string
 *                 example: Unauthorized attempt, login again!
 *
 * paths:
 *   /api/v1/quiz:
 *     get:
 *       summary: Get all quizs
 *       tags: [quizs]
 *       parameters:
 *         - in: query
 *           name: page
 *           schema:
 *             type: integer
 *             default: 1
 *           description: Page number for pagination
 *         - in: query
 *           name: limit
 *           schema:
 *             type: integer
 *             default: 10
 *           description: Number of items per page
 *       responses:
 *         200:
 *           description: List of quizs
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: quizs retrieved successfully
 *                   data:
 *                     type: object
 *                     properties:
 *                       quizs:
 *                         type: array
 *                         items:
 *                           $ref: '#/components/schemas/quiz'
 *                       pagination:
 *                         type: object
 *                         properties:
 *                           total:
 *                             type: integer
 *                             description: Total number of quizs
 *                           page:
 *                             type: integer
 *                             description: Current page number
 *                           limit:
 *                             type: integer
 *                             description: Number of items per page
 *                           pages:
 *                             type: integer
 *                             description: Total number of pages
 *
 *   /api/v1/quiz/{id}:
 *     get:
 *       summary: Get quiz by ID
 *       tags: [quizs]
 *       parameters:
 *         - $ref: '#/components/parameters/quizId'
 *       responses:
 *         200:
 *           description: quiz retrieved successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: quiz retrieved successfully
 *                   data:
 *                     $ref: '#/components/schemas/quiz'
 *         404:
 *           description: quiz not found
 *
 *   /api/v1/admin/quiz:
 *     post:
 *       summary: Create a new quiz
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/CreatequizRequest'
 *       responses:
 *         201:
 *           description: quiz created successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 201
 *                   message:
 *                     type: string
 *                     example: quiz created successfully
 *                   data:
 *                     $ref: '#/components/schemas/quiz'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *
 *   /api/v1/admin/quiz/{id}:
 *     patch:
 *       summary: Update a quiz
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/quizId'
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/UpdatequizRequest'
 *       responses:
 *         200:
 *           description: quiz updated successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: quiz updated successfully
 *                   data:
 *                     $ref: '#/components/schemas/quiz'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: quiz not found
 *
 *     delete:
 *       summary: Delete a quiz
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/quizId'
 *       responses:
 *         200:
 *           description: quiz deleted successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: quiz deleted successfully
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: quiz not found
 */