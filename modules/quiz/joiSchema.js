const Joi = require("joi");

module.exports = {
  create: Joi.object().keys({
    name: Joi.string().required(),
    // nameType: Joi.string().required(),
    description: Joi.string().required(),
    isActive: Joi.boolean(),
    questions: Joi.array().items(
      Joi.object({
        question: Joi.string().required(),
        questionType: Joi.string().required(),
        explanation: Joi.string().optional(),
        options: Joi.array().items(
          Joi.object({
            isCorrect: Joi.boolean().required(),
            optionText: Joi.string().required(),
          })
        ).optional(),
      })
    ).required(),
  }),
  update: Joi.object().keys({
    name: Joi.string(),
    // nameType: Joi.string(),
    description: Joi.string(),
    isActive: Joi.boolean(),
  }),
};
