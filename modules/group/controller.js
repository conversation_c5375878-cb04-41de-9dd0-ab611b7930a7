"use strict";
const GroupService = require("./service");
const { sqquery } = require("../../utils/query");
const Language = require("../language/model");
const { getFilePath } = require("../../middlewares/multer/helper");
const sequelize = require("../../config/db");

exports.getAll = async (req, res, next) => {
  try {
    const data = await GroupService.findAndCountAll({
      ...sqquery(req.query, {}, ["name", "description"]),
      attributes: [
        "id",
        "name",
        "description",
        "image",
        "isActive",
        "isPremium",
        "languageId",
        [
          sequelize.literal(
            `(SELECT COUNT(*) FROM "${process.env.DEVSCHEMA}"."groupFlashcards" WHERE "groupFlashcards"."groupId" = "group"."id")`
          ),
          "flashcardcounts",
        ],
      ],
      include: [
        {
          model: Language,
          required: true,
          attributes: ["code", "name", "englishName"],
        },
      ],
    });

    res.status(200).send({
      status: "success",
      data,
    });
  } catch (error) {
    next(error);
  }
};
exports.add = async (req, res, next) => {
  try {
    const courseImagePath = req?.files?.image
      ? getFilePath(req.files.image[0])
      : null;

    if (courseImagePath) {
      req.body.image = courseImagePath;
    }
    const data = await GroupService.create(req.body);

    res.status(200).json({
      status: "success",
      data,
    });
  } catch (error) {
    // console.error(error);
    next(error);
  }
};

exports.getById = async (req, res, next) => {
  try {
    const data = await GroupService.findOne({
      where: {
        id: req.params.id,
      },
    });

    res.status(200).send({
      status: "success",
      data,
    });
  } catch (error) {
    next(error);
  }
};

exports.update = async (req, res, next) => {
  try {
    const courseImagePath = req?.files?.image
      ? getFilePath(req.files.image[0])
      : null;

    if (courseImagePath) {
      req.body.image = courseImagePath;
    }
    const [affectedRows] = await GroupService.update(req.body, {
      where: {
        id: req.params.id,
      },
    });

    res.status(200).json({
      status: "success",
      data: {
        affectedRows,
      },
    });
  } catch (error) {
    next(error);
  }
};

exports.delete = async (req, res, next) => {
  try {
    const affectedRows = await GroupService.delete({
      where: {
        id: req.params.id,
      },
    });

    res.status(200).send({
      status: "success",
      data: {
        affectedRows,
      },
    });
  } catch (error) {
    next(error);
  }
};
