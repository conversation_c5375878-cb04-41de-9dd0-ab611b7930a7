"use strict";
const { DataTypes } = require("sequelize");
const { v4: uuidv4 } = require("uuid");
const sequelize = require("../../config/db");
const Language = require("../language/model");

const Group = sequelize.define(
  "group",
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: uuidv4,
      allowNull: false,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    image: {
      type: DataTypes.STRING,
      allowNull: true,
    }, 
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    isPremium: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
  },
  {
    paranoid: true,
  }
);

// Relationships
Language.hasMany(Group, { foreignKey: { allowNull: false } });
Group.belongsTo(Language);

module.exports = Group;
