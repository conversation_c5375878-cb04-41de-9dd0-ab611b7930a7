const Joi = require("joi");

module.exports = {
  create: Joi.object().keys({
    name: Joi.string().required(),
    description: Joi.string().required(),
    isActive: Joi.boolean(),
    isPremium: Joi.boolean(),
    languageId: Joi.string().guid({ version: "uuidv4" }).required(),
    // levelId: Joi.string().guid({ version: "uuidv4" }).required(),
  }),
  update: Joi.object().keys({
    name: Joi.string(),
    description: Joi.string(),
    isActive: Joi.boolean(),
    isPremium: Joi.boolean(),
    languageId: Joi.string().guid({ version: "uuidv4" }),
    // levelId: Joi.string().guid({ version: "uuidv4" }),
  }),
};
