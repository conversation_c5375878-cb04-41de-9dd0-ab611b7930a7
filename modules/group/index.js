"use strict";

const router = require("express").Router();
const group = require("./controller");
// const { authMiddleware, protectRoute } = require("../../middlewares/auth");
// const { joiValidator } = require("../../middlewares/joiValidator");

router
  .route("/")
  .get(group.getAll)

//   .post(joiValidator(joiSchema.create), group.add);
router
  .route("/:id")
  .get(group.getById)
//   .patch(joiValidator(joiSchema.update), group.update)
//   .delete(group.delete);

module.exports = router;
