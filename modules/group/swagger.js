/**
 * @swagger
 * components:
 *   schemas:
 *     group:
 *       type: object
 *       required:
 *         - .required()
         - .required()
 *       properties:
 *         id:
           type: string
           description: The id of the group
         defaultValue:
           type: string
           description: The defaultValue of the group
         allowNull:
           type: string
           description: The allowNull of the group
         primaryKey:
           type: string
           description: The primaryKey of the group
         :
           type: string
           description: The  of the group
 *       example:
 *         id: "Example id"
         defaultValue: "Example defaultValue"
         allowNull: "Example allowNull"
         primaryKey: "Example primaryKey"
         : "Example "
 *
 *     CreategroupRequest:
 *       type: object
 *       required:
 *         - .required()
         - .required()
 *       properties:
 *         name:
           type: string
         description:
           type: string
         isActive:
           type: string
         isPremium:
           type: string
         languageId:
           type: string
 *       example:
 *         name: "Example name"
         description: "Example description"
         isActive: "Example isActive"
         isPremium: "Example isPremium"
         languageId: "Example languageId"
 *
 *     UpdategroupRequest:
 *       type: object
 *       properties:
 *         name:
           type: string
         description:
           type: string
         isActive:
           type: string
         isPremium:
           type: string
         languageId:
           type: string
 *       example:
 *         name: "Example name"
         description: "Example description"
         isActive: "Example isActive"
         isPremium: "Example isPremium"
         languageId: "Example languageId"
 *
 *   parameters:
 *     groupId:
 *       in: path
 *       name: id
 *       required: true
 *       schema:
 *         type: string
 *         format: uuid
 *       description: The group ID
 *
 *   responses:
 *     UnauthorizedError:
 *       description: Access token is missing or invalid
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: integer
 *                 example: 401
 *               message:
 *                 type: string
 *                 example: Unauthorized attempt, login again!
 *
 * paths:
 *   /api/v1/group:
 *     get:
 *       summary: Get all groups
 *       tags: [groups]
 *       parameters:
 *         - in: query
 *           name: page
 *           schema:
 *             type: integer
 *             default: 1
 *           description: Page number for pagination
 *         - in: query
 *           name: limit
 *           schema:
 *             type: integer
 *             default: 10
 *           description: Number of items per page
 *       responses:
 *         200:
 *           description: List of groups
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: groups retrieved successfully
 *                   data:
 *                     type: object
 *                     properties:
 *                       groups:
 *                         type: array
 *                         items:
 *                           $ref: '#/components/schemas/group'
 *                       pagination:
 *                         type: object
 *                         properties:
 *                           total:
 *                             type: integer
 *                             description: Total number of groups
 *                           page:
 *                             type: integer
 *                             description: Current page number
 *                           limit:
 *                             type: integer
 *                             description: Number of items per page
 *                           pages:
 *                             type: integer
 *                             description: Total number of pages
 *
 *   /api/v1/group/{id}:
 *     get:
 *       summary: Get group by ID
 *       tags: [groups]
 *       parameters:
 *         - $ref: '#/components/parameters/groupId'
 *       responses:
 *         200:
 *           description: group retrieved successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: group retrieved successfully
 *                   data:
 *                     $ref: '#/components/schemas/group'
 *         404:
 *           description: group not found
 *
 *   /api/v1/admin/group:
 *     post:
 *       summary: Create a new group
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/CreategroupRequest'
 *       responses:
 *         201:
 *           description: group created successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 201
 *                   message:
 *                     type: string
 *                     example: group created successfully
 *                   data:
 *                     $ref: '#/components/schemas/group'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *
 *   /api/v1/admin/group/{id}:
 *     patch:
 *       summary: Update a group
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/groupId'
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/UpdategroupRequest'
 *       responses:
 *         200:
 *           description: group updated successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: group updated successfully
 *                   data:
 *                     $ref: '#/components/schemas/group'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: group not found
 *
 *     delete:
 *       summary: Delete a group
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/groupId'
 *       responses:
 *         200:
 *           description: group deleted successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: group deleted successfully
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: group not found
 */