"use strict";

const router = require("express").Router();
const group = require("./controller");
const joiSchema = require("./joiSchema");
const { joiValidator } = require("../../middlewares/joiValidator");
const { setUploadFolder } = require("../../middlewares/multer/helper");
const { upload } = require("../../middlewares/multer");
// const { authMiddleware, protectRoute } = require("../../middlewares/auth");


router
  .route("/")
  .get(group.getAll)
  .post(
    setUploadFolder("group"),
    upload.fields([{ name: "image", maxCount: 1 }]),
    joiValidator(joiSchema.create),
    group.add
  );
router
  .route("/:id")
  .get(group.getById)
  .patch(
    setUploadFolder("group"),
    upload.fields([{ name: "image", maxCount: 1 }]),
    joiValidator(joiSchema.update),
    group.update
  )
  .delete(group.delete);

module.exports = router;
