const Joi = require("joi");

module.exports = {
  create: Joi.object().keys({
    position: Joi.number().required(),
    groupId: Joi.string().guid({ version: "uuidv4" }).required(),
    flashcardId: Joi.string().guid({ version: "uuidv4" }).required(),
  }),
  update: Joi.object().keys({
    position: Joi.number(),
    groupId: Joi.string().guid({ version: "uuidv4" }),
    flashcardId: Joi.string().guid({ version: "uuidv4" }),
  }),
};
