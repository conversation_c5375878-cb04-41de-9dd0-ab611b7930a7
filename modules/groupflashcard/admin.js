"use strict";

const router = require("express").Router();
const groupFlashcard = require("./controller");
const joiSchema = require("./joiSchema");
const { joiValidator } = require("../../middlewares/joiValidator");
// const { authMiddleware, protectRoute } = require("../../middlewares/auth");


router
  .route("/")
  .get(groupFlashcard.getAll)
  .post(joiValidator(joiSchema.create), groupFlashcard.add);
router
  .route("/:id")
  .get(groupFlashcard.getById)
  .patch(joiValidator(joiSchema.update), groupFlashcard.update)
  .delete(groupFlashcard.delete);

module.exports = router;
