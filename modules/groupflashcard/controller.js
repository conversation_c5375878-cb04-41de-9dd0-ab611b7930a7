"use strict";
const GroupFlashcardService = require("./service");
const { sqquery } = require("../../utils/query");
const createHttpError = require("http-errors");
const Flashcard = require("../flashcard/model");

exports.getAll = async (req, res, next) => {
  try {
    const data = await GroupFlashcardService.findAndCountAll(
      {...sqquery(req.query,), include: [
        {
          model: Flashcard,
          required: true,
          attributes: [
            "id",
            "word",
            "pronunciation",
            "meaning",
            "isActive",
            "createdAt",
            "languageId",
            "example",
            "image",
            "audio",]
        },
      ], }
    );

    res.status(200).send({
      status: "success",
      data,
    });
  } catch (error) {
    next(error);
  }
};
exports.add = async (req, res, next) => {
  try {
    const exist = await GroupFlashcardService.findOne({
      where: {
        groupId: req?.body?.groupId,
        flashcardId: req?.body?.flashcardId,
      },
    });
    if (exist) {
      throw createHttpError(400, `Flashcard already exists in group`);
    }
    const data = await GroupFlashcardService.create(req.body);

    res.status(200).json({
      status: "success",
      data,
    });
  } catch (error) {
    // console.error(error);
    next(error);
  }
};

exports.getById = async (req, res, next) => {
  try {
    const data = await GroupFlashcardService.findOne({
      where: {
        id: req.params.id,
      },
    });

    res.status(200).send({
      status: "success",
      data,
    });
  } catch (error) {
    next(error);
  }
};

exports.update = async (req, res, next) => {
  try {
    const [affectedRows] = await GroupFlashcardService.update(req.body, {
      where: {
        id: req.params.id,
      },
    });

    res.status(200).json({
      status: "success",
      data: {
        affectedRows,
      },
    });
  } catch (error) {
    next(error);
  }
};

exports.delete = async (req, res, next) => {
  try {
    const affectedRows = await GroupFlashcardService.delete({
      where: {
        id: req.params.id,
      },
    });

    res.status(200).send({
      status: "success",
      data: {
        affectedRows,
      },
    });
  } catch (error) {
    next(error);
  }
};
