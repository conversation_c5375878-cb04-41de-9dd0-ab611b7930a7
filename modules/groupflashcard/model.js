"use strict";
const { DataTypes } = require("sequelize");
const { v4: uuidv4 } = require("uuid");
const sequelize = require("../../config/db");
const Flashcard = require("../flashcard/model");
const Group = require("../group/model");

const GroupFlashcard = sequelize.define(
  "groupFlashcard",
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: uuidv4,
      allowNull: false,
      primaryKey: true,
    },
    position: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
  },
  {
    paranoid: true,
  }
);

// Relationships
Group.hasMany(GroupFlashcard, { foreignKey: { allowNull: false } });
GroupFlashcard.belongsTo(Group);

Flashcard.hasMany(GroupFlashcard, { foreignKey: { allowNull: false } });
GroupFlashcard.belongsTo(Flashcard);

module.exports = GroupFlashcard;
