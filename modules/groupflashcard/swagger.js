/**
 * @swagger
 * components:
 *   schemas:
 *     groupflashcard:
 *       type: object
 *       required:
 *         - .required()
 *       properties:
 *         id:
           type: string
           description: The id of the groupflashcard
         defaultValue:
           type: string
           description: The defaultValue of the groupflashcard
         allowNull:
           type: string
           description: The allowNull of the groupflashcard
         primaryKey:
           type: string
           description: The primaryKey of the groupflashcard
         :
           type: string
           description: The  of the groupflashcard
 *       example:
 *         id: "Example id"
         defaultValue: "Example defaultValue"
         allowNull: "Example allowNull"
         primaryKey: "Example primaryKey"
         : "Example "
 *
 *     CreategroupflashcardRequest:
 *       type: object
 *       required:
 *         - .required()
 *       properties:
 *         position:
           type: string
         groupId:
           type: string
 *       example:
 *         position: "Example position"
         groupId: "Example groupId"
 *
 *     UpdategroupflashcardRequest:
 *       type: object
 *       properties:
 *         position:
           type: string
         groupId:
           type: string
 *       example:
 *         position: "Example position"
         groupId: "Example groupId"
 *
 *   parameters:
 *     groupflashcardId:
 *       in: path
 *       name: id
 *       required: true
 *       schema:
 *         type: string
 *         format: uuid
 *       description: The groupflashcard ID
 *
 *   responses:
 *     UnauthorizedError:
 *       description: Access token is missing or invalid
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: integer
 *                 example: 401
 *               message:
 *                 type: string
 *                 example: Unauthorized attempt, login again!
 *
 * paths:
 *   /api/v1/groupflashcard:
 *     get:
 *       summary: Get all groupflashcards
 *       tags: [groupflashcards]
 *       parameters:
 *         - in: query
 *           name: page
 *           schema:
 *             type: integer
 *             default: 1
 *           description: Page number for pagination
 *         - in: query
 *           name: limit
 *           schema:
 *             type: integer
 *             default: 10
 *           description: Number of items per page
 *       responses:
 *         200:
 *           description: List of groupflashcards
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: groupflashcards retrieved successfully
 *                   data:
 *                     type: object
 *                     properties:
 *                       groupflashcards:
 *                         type: array
 *                         items:
 *                           $ref: '#/components/schemas/groupflashcard'
 *                       pagination:
 *                         type: object
 *                         properties:
 *                           total:
 *                             type: integer
 *                             description: Total number of groupflashcards
 *                           page:
 *                             type: integer
 *                             description: Current page number
 *                           limit:
 *                             type: integer
 *                             description: Number of items per page
 *                           pages:
 *                             type: integer
 *                             description: Total number of pages
 *
 *   /api/v1/groupflashcard/{id}:
 *     get:
 *       summary: Get groupflashcard by ID
 *       tags: [groupflashcards]
 *       parameters:
 *         - $ref: '#/components/parameters/groupflashcardId'
 *       responses:
 *         200:
 *           description: groupflashcard retrieved successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: groupflashcard retrieved successfully
 *                   data:
 *                     $ref: '#/components/schemas/groupflashcard'
 *         404:
 *           description: groupflashcard not found
 *
 *   /api/v1/admin/groupflashcard:
 *     post:
 *       summary: Create a new groupflashcard
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/CreategroupflashcardRequest'
 *       responses:
 *         201:
 *           description: groupflashcard created successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 201
 *                   message:
 *                     type: string
 *                     example: groupflashcard created successfully
 *                   data:
 *                     $ref: '#/components/schemas/groupflashcard'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *
 *   /api/v1/admin/groupflashcard/{id}:
 *     patch:
 *       summary: Update a groupflashcard
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/groupflashcardId'
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/UpdategroupflashcardRequest'
 *       responses:
 *         200:
 *           description: groupflashcard updated successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: groupflashcard updated successfully
 *                   data:
 *                     $ref: '#/components/schemas/groupflashcard'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: groupflashcard not found
 *
 *     delete:
 *       summary: Delete a groupflashcard
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/groupflashcardId'
 *       responses:
 *         200:
 *           description: groupflashcard deleted successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: groupflashcard deleted successfully
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: groupflashcard not found
 */