const express = require("express");
const { validateAP<PERSON><PERSON><PERSON> } = require("../middlewares/auth");
const router = express.Router();
const { listRoutes } = require('route-snapshot');
// router.use("/api/v1/admin",validate<PERSON><PERSON><PERSON><PERSON>, require("./admin"));
// router.use("/api/v1",validateAP<PERSON><PERSON><PERSON>, require("./user"));

router.use("/api/v1/admin", require("./admin"));
router.use("/api/v1", require("./user"));

router.route("/all-routes").get((req,res)=>{
    const routes = listRoutes(req.app);
    res.json(routes);
})

module.exports = router;
