const express = require("express");
const { authMiddleware, ROLES, protectRoute } = require("../middlewares/auth");
const router = express.Router();

router.use("/appConfig", require("../modules/appConfig"));
router.use("/user", require("../modules/user"));
router.use(authMiddleware, protectRoute([ROLES.USER]));
router.use("/cron", require("../modules/cronjob"));
router.use("/notification", require("../modules/notification"));
router.use("/language", require("../modules/language"));
// router.use("/category", require("../modules/category"));
router.use("/course", require("../modules/course"));
router.use("/courseflashcard", require("../modules/courseflashcard"));
router.use("/coursequiz", require("../modules/coursequiz"));
router.use("/flashcard", require("../modules/flashcard"));
router.use("/flashcardbookmark", require("../modules/flashcardbookmark"));
router.use("/flashcardlike", require("../modules/flashcardlike"));
router.use("/flashcarddislike", require("../modules/flashcarddislike"));
router.use("/group", require("../modules/group"));
router.use("/groupflashcard", require("../modules/groupflashcard"));
router.use("/level", require("../modules/level"));
router.use("/proposecard", require("../modules/proposecard"));
router.use("/quiz", require("../modules/quiz"));
router.use("/questionanswer", require("../modules/questionanswer"));
router.use("/questionoption", require("../modules/questionoption"));
router.use("/userflashcard", require("../modules/userflashcard"));
router.use("/usergroup", require("../modules/usergroup"));
router.use("/usercourse", require("../modules/usercourse"));
router.use("/userlanguage", require("../modules/userlanguage"));
router.use("/support", require("../modules/support"));

module.exports = router;
