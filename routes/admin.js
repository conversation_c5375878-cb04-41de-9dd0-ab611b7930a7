const express = require("express");
const router = express.Router();
const { flushAll } = require("../utils/redis");
const { refillData } = require("../utils/elastic");
const { authMiddleware, protectRoute, ROLES } = require("../middlewares/auth");

// router.use("/", require("../modules/admin"));
router.use("/flush", flushAll);
router.use("/refill", refillData);

router.use("/user", require("../modules/user/admin"));
router.use(authMiddleware, protectRoute([ROLES.ADMIN, ROLES.SUPERADMIN]));
router.use("/appConfig", require("../modules/appConfig/admin"));
router.use("/dashboard", require("../modules/dashboard/admin"));
router.use("/notification", require("../modules/notification/admin"));
router.use("/language", require("../modules/language/admin"));
// router.use("/category", require("../modules/category/admin"));
router.use("/course", require("../modules/course/admin"));
router.use("/courseflashcard", require("../modules/courseflashcard/admin"));
router.use("/coursequiz", require("../modules/coursequiz/admin"));
router.use("/flashcard", require("../modules/flashcard/admin"));
router.use("/similarWord", require("../modules/similarWord/admin"));
router.use("/flashcardbookmark", require("../modules/flashcardbookmark/admin"));
router.use("/flashcardlike", require("../modules/flashcardlike/admin"));
router.use("/flashcarddislike", require("../modules/flashcarddislike/admin"));
router.use("/group", require("../modules/group/admin"));
router.use("/groupflashcard", require("../modules/groupflashcard/admin"));
router.use("/level", require("../modules/level/admin"));
router.use("/proposecard", require("../modules/proposecard/admin"));
router.use("/quiz", require("../modules/quiz/admin"));
router.use("/question", require("../modules/question/admin"));
router.use("/questionanswer", require("../modules/questionanswer/admin"));
router.use("/questionoption", require("../modules/questionoption/admin"));
router.use("/userflashcard", require("../modules/userflashcard/admin"));
router.use("/usergroup", require("../modules/usergroup/admin"));
router.use("/usercourse", require("../modules/usercourse/admin"));
router.use("/userlanguage", require("../modules/userlanguage/admin"));
router.use("/groupreport", require("../modules/groupreport/admin"));
router.use("/coursereport", require("../modules/coursereport/admin"));
router.use("/flashcardreport", require("../modules/flashcardreport/admin"));
router.use("/support", require("../modules/support/admin"));
router.use("/api-logs", require("../modules/api-logs/admin"));

module.exports = router;
