const multerS3 = require("multer-s3");
const multer = require("multer");
const path = require("path");
const { s3 } = require("../../config/aws");
const { v4: uuidv4 } = require("uuid");

// Blacklisted of permitted file extensions
const restrictedExtensions = [".txt", ".html", ".exe", ".sh", ".php"];

const uploadToS3 = multer({
  storage: multerS3({
    s3,
    bucket: process.env.BUCKET,
    metadata: function (req, file, cb) {
      // Add custom metadata or validation here if needed
      const metadata = {
        fieldName: file.fieldname,
        "Cache-Control": "public, max-age=31536000", // Adjust the max-age as needed
        "Content-Disposition": `inline; filename="${file.originalname}"`,
      };
      cb(null, metadata);
    },

    key: function (req, file, cb) {
      // Check the file extension against the Blacklist
      const originalExtension = path.extname(file.originalname).toLowerCase();
      if (restrictedExtensions.includes(originalExtension)) {
        return cb(new Error("Invalid file extension"), false);
      }
      // Generate a unique file name for the S3 object
      const uniqueFileName = uuidv4();
      const key = `${uniqueFileName}${originalExtension}`;
      cb(null, key);
    },

    contentType: multerS3.AUTO_CONTENT_TYPE, // Automatically detect content type based on file extension
  }),
});

module.exports = {uploadToS3}