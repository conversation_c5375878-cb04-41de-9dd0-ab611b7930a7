const { s3 } = require("../../config/aws");

const deleteFilesFromS3 = (urls) => {
  try {
    const keys = urls.map((url) => url.split(`${process.env.BUCKET_URL}/`)[1]);

    if (keys.length === 0) {
      return; // No files to delete, return early
    }

    const payload = {
      Bucket: process.env.BUCKET,
      Delete: {
        Objects: keys.map((Key) => ({ Key })),
        Quiet: true,
      },
    };

    s3.deleteObjects(payload, (err, data) => {
      if (err) {
        console.error("Error deleting objects:", err);
        throw err; // Throw the error to be caught and handled by the caller
      } else {
        console.log("Files deleted successfully", data.Deleted);
      }
    });
  } catch (error) {
    console.error(error);
    next(error); // Pass the error to Express error handling
  }
};

module.exports = { deleteFilesFromS3 };
