// multer/index.js
const { uploadToS3, deleteFilesFromS3 ,uploadFileToS3PathBased} = require("../multer/multer-s3");
const { uploadToCloudinary, deleteFilesFromCloudinary ,uploadFileToCloudnaryPathBased} = require("../multer/multer-cloudinary");

const storage = process.env.STORAGE_PROVIDER || "s3"; // Default to S3 if not specified

let upload = uploadToS3;
let deleteFiles = deleteFilesFromS3;
let uploadFilePathBased = uploadFileToS3PathBased
switch (storage) {
  case "cloudinary":
    upload = uploadToCloudinary;
    deleteFiles = deleteFilesFromCloudinary;
    uploadFilePathBased =uploadFileToCloudnaryPathBased
    break;
  case "s3":
  default:
    upload = uploadToS3;
    deleteFiles = deleteFilesFromS3;
    uploadFilePathBased =uploadFileToS3PathBased
    break;
}
console.log(uploadToCloudinary,"Cloudinary Config")

module.exports = { upload, deleteFiles,uploadFilePathBased };
