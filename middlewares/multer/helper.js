// /multer/helper.js

const setUploadFolder = (folderName) => (req, res, next) => {
  req.locals = req.locals || {};
  req.locals.uploadFolder = folderName;
  next();
};
const getFilePath = (file) => {
const storage = process.env.STORAGE_PROVIDER || "s3"; // Default to S3 if not specified

  if (storage === 'cloudinary') {
    // Cloudinary typically returns URLs directly
    return file?.path || null;
  } else if (storage === 's3') {
    // S3 may require constructing the URL
    return file?.location || null; // Assuming `location` contains the S3 URL
  }
  return null;
};

const getOriginalFileName = (file) => {
  if (!file || !file.originalname) return null;
  // Decode the filename to handle any encoded characters
  return Buffer.from(file.originalname, 'binary').toString();
};
const sanitizePublicId=(publicId)=> {
  return publicId.replace(/[^a-zA-Z0-9-_./]/g, ''); // Remove invalid characters
}


module.exports = {setUploadFolder,getFilePath,getOriginalFileName,sanitizePublicId};
