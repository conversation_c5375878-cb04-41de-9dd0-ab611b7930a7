const multer = require("multer");
const { v2: cloudinary } = require("cloudinary");
const { CloudinaryStorage } = require("multer-storage-cloudinary");
const { getOriginalFileName, sanitizePublicId } = require("./helper");
const path = require('path');

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

// Cloudinary storage configuration without cropping or forced format
const storage = new CloudinaryStorage({
  cloudinary,
  params: {
    folder: (req, file) =>  req?.locals?.uploadFolder ? `${req?.locals?.uploadFolder}/${file?.fieldname}` : "default",
    public_id: (req, file) =>{
      const originalName = sanitizePublicId(getOriginalFileName(file));
      const nameWithoutExtension = originalName?.split('.').slice(0, -1).join('.');
      return`${nameWithoutExtension}-${Date.now()}`},
    // allowed_formats: ['jpg', 'png', 'gif', 'mp3', 'wav'],
    resource_type: 'auto',
  },
});

const uploadToCloudinary = multer({ storage });

const deleteFilesFromCloudinary = (urls) => {
  try {
    const publicIds = urls.map((url) => {
      const parts = url.split("/");
      return parts[parts.length - 1].split(".")[0];
    });

    publicIds.forEach((public_id) => {
      cloudinary.uploader.destroy(public_id, (error, result) => {
        if (error) console.error("Error deleting file from Cloudinary:", error);
        else console.log("File deleted from Cloudinary:", result);
      });
    });
  } catch (error) {
    console.error(error);
    throw error;
  }
};
const uploadFileToCloudnaryPathBased = async (filePath,folder) => {
  try {
    const fileName = sanitizePublicId(path.basename(filePath));
    console.log(fileName,"fileName fileNamefileName")
    const result = await cloudinary.uploader.upload(filePath, {
      folder: folder ?? 'default',
      resource_type: 'auto',
      public_id: `${Date.now()}_${fileName}`,
    });
    return result.secure_url;
  } catch (err) {
    console.error('Error uploading audio to Cloudinary:', err);
    throw new Error('Error uploading audio to Cloudinary');
  }
};

module.exports = { uploadToCloudinary, deleteFilesFromCloudinary,uploadFileToCloudnaryPathBased };
