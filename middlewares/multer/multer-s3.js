// multer/multer-s3.js
const multerS3 = require("multer-s3");
const multer = require("multer");
const path = require("path");
const { s3 } = require("../../config/aws");
const { v4: uuidv4 } = require("uuid");
const { sanitizePublicId } = require("./helper");

// Blacklisted file extensions
const restrictedExtensions = [".txt", ".html", ".exe", ".sh", ".php"];

const uploadToS3 = multer({
  storage: multerS3({
    s3,
    bucket: process.env.BUCKET,
    metadata: function (req, file, cb) {
      const metadata = {
        fieldName: file.fieldname,
        "Cache-Control": "public, max-age=31536000",
        "Content-Disposition": `inline; filename="${file.originalname}"`,
      };
      cb(null, metadata);
    },
    key: function (req, file, cb) {
      const originalExtension = path.extname(file.originalname).toLowerCase();
      if (restrictedExtensions.includes(originalExtension)) {
        return cb(new Error("Invalid file extension"), false);
      }

      const folder = req?.locals?.uploadFolder || "flashcard";
      const uniqueFileName = uuidv4();
      const key = `${folder}/${uniqueFileName}${originalExtension}`;
      cb(null, key);
    },
    contentType: multerS3.AUTO_CONTENT_TYPE,
  }),
});

const deleteFilesFromS3 = (urls) => {
  try {
    const keys = urls.map((url) => url.split(`${process.env.BUCKET_URL}/`)[1]);
    if (keys.length === 0) return;

    const payload = {
      Bucket: process.env.BUCKET,
      Delete: {
        Objects: keys.map((Key) => ({ Key })),
        Quiet: true,
      },
    };

    s3.deleteObjects(payload, (err, data) => {
      if (err) {
        console.error("Error deleting objects:", err);
        throw err;
      } else {
        console.log("Files deleted successfully", data.Deleted);
      }
    });
  } catch (error) {
    console.error(error);
    throw error;
  }
};

const uploadFileToS3PathBased = async (filePath, folder = 'default') => {
  try {
    const uniqueFileName = uuidv4();

    const fileStream = fs.createReadStream(filePath);
    const fileName = sanitizePublicId(path.basename(filePath)); // Use the base name of the file path
    const key = `${folder}/${uniqueFileName}${fileName}`;

    await s3.putObject({
      Bucket: process.env.BUCKET,
      Key: key,
      Body: fileStream,
      // ContentType is not provided, allowing S3 to infer the type based on the file
    });

    return `https://${process.env.BUCKET}.s3.${process.env.AWS_REGION}.amazonaws.com/${key}`;
  } catch (err) {
    console.error('Error uploading file to S3:', err);
    throw new Error('Error uploading file to S3');
  }
};


module.exports = { uploadToS3, deleteFilesFromS3,uploadFileToS3PathBased };
