const multerS3 = require("multer-s3");
const multer = require("multer");
const path = require("path");
const { s3 } = require("../config/aws");
const { v4: uuidv4 } = require("uuid");

// Blacklisted of permitted file extensions
const restrictedExtensions = [".txt", ".html", ".exe", ".sh", ".php"];

const upload = multer({
  storage: multerS3({
    s3,
    bucket: process.env.BUCKET,
    metadata: function (req, file, cb) {
      try {
        // Add custom metadata or validation here if needed
        const metadata = {
          fieldName: file.fieldname,
          "Cache-Control": "public, max-age=31536000", // Adjust the max-age as needed
          // Use UTF-8 encoding for the filename to handle non-ASCII characters correctly
          "Content-Disposition": `inline; filename*=UTF-8''${encodeURIComponent(file.originalname)}`,
        };
        cb(null, metadata);
      } catch (error) {
        cb(error);
      }
    },

    key: function (req, file, cb) {
      try {
        // Check the file extension against the Blacklist
        const originalExtension = path.extname(file.originalname).toLowerCase();
        if (restrictedExtensions.includes(originalExtension)) {
          return cb(new Error("Invalid file extension"), false);
        }
        // Generate a unique file name for the S3 object
        const uniqueFileName = uuidv4();
        const key = `${uniqueFileName}${originalExtension}`;
        cb(null, key);
      } catch (error) {
        cb(error);
      }
    },

    contentType: multerS3.AUTO_CONTENT_TYPE, // Automatically detect content type based on file extension
  }),
  limits: {
    fileSize: 5 * 1024 * 1024, // Optional: Set a file size limit
  },
});

const deleteFilesFromS3 = async (urls) => {
  try {
    const keys = urls.map((url) => url.split(`${process.env.BUCKET_URL}/`)[1]);

    if (keys.length === 0) {
      return; // No files to delete, return early
    }

    const payload = {
      Bucket: process.env.BUCKET,
      Delete: {
        Objects: keys.map((Key) => ({ Key })),
        Quiet: true,
      },
    };

    // Use the promise-based method to handle the deletion
    const data = await s3.deleteObjects(payload).promise();
    console.log("Files deleted successfully", data.Deleted);
    return data.Deleted;
  } catch (error) {
    console.error("Error deleting objects:", error);
    throw error; // Throw the error to be caught and handled by the caller
  }
};

module.exports = { upload, deleteFilesFromS3 };
