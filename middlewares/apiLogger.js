const ApiLog = require("../models/ApiLog");
const { jwtDecoder } = require("../utils/service");

const apiLogger = async (req, res, next) => {
  const start = Date.now();
  const originalSend = res.send;
  let responseBody;

  // Capture response body
  res.send = function (body) {
    responseBody = body;
    return originalSend.call(this, body);
  };

  // After response is sent
  res.on("finish", async () => {
    try {
      const duration = Date.now() - start;
      const token = req.headers.authorization?.split(" ")[1];
      const user = req.user || {};
      const ip =
        req.headers["x-forwarded-for"]?.split(",")[0] ||
        req.connection.remoteAddress ||
        req.ip;

      let jwtUser = null;
      if (token) {
        jwtUser = await jwtDecoder(token);
      }

      const logData = {
        method: req.method,
        path: req.originalUrl,
        userId: user.id || jwtUser?.id || null,
        jwtUser: jwtUser || null,
        userEmail: user.email || jwtUser?.email || null,
        payload: {
          body: req.body,
          query: req.query,
          params: req.params,
        },
        response: responseBody ? JSON.parse(responseBody) : null,
        statusCode: res.statusCode,
        ipAddress: ip,
        userAgent: req.get("user-agent"),
        duration,
      };
      // not log api logs for api-logs route
      // as well not log for OPTIONS method
      if (!req.originalUrl.includes("/api-logs") && req.method !== "OPTIONS") {
        console.log("API LOG", logData);
        await ApiLog.create(logData);
      }
    } catch (error) {
      console.error("Error logging API request:", error);
    }
  });

  next();
};

module.exports = apiLogger;
