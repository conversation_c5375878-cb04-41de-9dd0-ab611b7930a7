const { Op } = require("sequelize");
const { dateFilter } = require("./service");

exports.getOpAttributeValue = (attribute, value) => {
  const operators = {
    gt: Op.gt,
    gte: Op.gte,
    lt: Op.lt,
    lte: Op.lte,
    eq: Op.eq,
    ne: Op.ne,
    notBetween: Op.notBetween,
    between: Op.between,
    in: Op.in,
    notIn: Op.notIn,
  };

  return operators[attribute] ? { [operators[attribute]]: value } : null;
};

exports.sqquery = (
  q,
  filter = {},
  searchFrom = [],
  excludeColumnsFromOrder = [],
  excludeFields = []
) => {
  // Preprocess query parameters
  q = exports.preprocessQueryParams(q);

  const limit = Math.max(1, parseInt(q?.limit) || 10000);
  const page = parseInt(q?.page) || 1;
  const skip = (page - 1) * limit;
  const sort = q?.sort || "createdAt";
  const sortBy = q?.sortBy || "DESC";
  const search = q?.search || "";

  let where = { ...dateFilter(q), ...filter };

  excludeFields.push(
    "page",
    "sort",
    "limit",
    "fields",
    "sortBy",
    "search",
    "startDate",
    "endDate"
  );
  excludeFields.forEach((el) => delete q[el]);

  Object.entries(q).forEach(([key, value]) => {
    if (typeof value === "object") {
      Object.entries(value).forEach(([op, val]) => {
        const obj = exports.getOpAttributeValue(op, val);
        if (obj) {
          const keyParts = key.split('.');
          if (keyParts.length > 1) {
            const lastPart = keyParts.pop();
            const nestedKey = keyParts.join('.');
            if (!where[nestedKey]) where[nestedKey] = {};
            where[nestedKey][lastPart] = obj;
          } else {
            where[key] = obj;
          }
        }
      });
    } else {
      where[key] = value;
    }
  });

  if (search && searchFrom.length) {
    const final = search.trim().replace(/\s+/g, "%");
    const searchData = searchFrom.map((columnName) => ({
      [columnName]: {
        [Op.like]: `%${final}%`,
      },
    }));

    where = Object.keys(where).length
      ? { ...where, [Op.or]: searchData }
      : { [Op.or]: searchData };
  }

  if (excludeColumnsFromOrder.includes(sort)) {
    return { where, limit, offset: skip };
  }

  return { where, order: [[sort, sortBy]], limit, offset: skip };
};

exports.usersqquery = (q) => {
  const limit = parseInt(q?.limit) || 10000;
  const page = parseInt(q?.page) || 1;
  const skip = (page - 1) * limit;
  const sort = q?.sort || "createdAt";
  const sortBy = q?.sortBy || "DESC";

  const order = [[sort, sortBy]];

  return { order, limit, offset: skip };
};

exports.preprocessQueryParams = (q) => {
  const processedQuery = {};
  Object.entries(q).forEach(([key, value]) => {
    if (key.startsWith('$')) {
      const newKey = key.slice(1);
      processedQuery[newKey] = value;
    } else {
      processedQuery[key] = value;
    }
  });
  return processedQuery;
};
