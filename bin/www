#!/usr/bin/env node

/**
 * Module dependencies.
 */

const app = require("../app");
const http = require("http");

// For Vercel deployment - export the app directly
if (process.env.VERCEL) {
  module.exports = app;
} else {
  // Only run server setup if not on Vercel
  startServer();
}

function startServer() {
  // Handle unhandled promise rejections
  process.on("unhandledRejection", async (reason, promise) => {
    try {
      console.error(JSON.stringify(reason));
      console.error(JSON.stringify(await promise));
    } catch (error) {
      console.error(error);
    }

    console.error(`Unhandled Rejection at: ${promise}. Reason: ${reason}`);
  });

  // Handle uncaught exceptions
  process.on("uncaughtException", (error) => {
    console.error("Uncaught Exception:", error);
    process.exit(1); // Exit the process after logging the uncaught exception
  });

  /**
   * Get port from environment and store in Express.
   */

  const port = normalizePort(process.env.PORT || "3001");
  app.set("port", port);

  /**
   * Create HTTP server.
   */

  const server = http.createServer(app);

  /**
   * Listen on provided port, on all network interfaces.
   */

  server.listen(port, () => {
    console.log(
      `Server is listening to port ${port} \x1b[${
        process.env.NODE_ENV === "production" ? "31m" : "32m"
      }[${process.env.NODE_ENV === "production" ? "PROD" : "DEV"}]\x1b[0m`
    );
  });

  server.on("error", onError);
  server.on("listening", onListening);

  /**
   * Normalize a port into a number, string, or false.
   */

  function normalizePort(val) {
    const port = parseInt(val, 10);

    if (isNaN(port)) {
      // named pipe
      return val;
    }

    if (port >= 0) {
      // port number
      return port;
    }

    return false;
  }

  /**
   * Event listener for HTTP server "error" event.
   */

  function onError(error) {
    if (error.syscall !== "listen") {
      throw error;
    }

    const bind = typeof port === "string" ? "Pipe " + port : "Port " + port;

    // handle specific listen errors with friendly messages
    switch (error.code) {
      case "EACCES":
        console.error(bind + " requires elevated privileges");
        break;
      case "EADDRINUSE":
        console.error(bind + " is already in use");
        break;
      default:
        console.error(error);
        throw error;
    }

    // Close server and exit process on error
    server.close(() => {
      console.error("Server closed due to error. Exiting process.");
      process.exit(1);
    });
  }

  /**
   * Event listener for HTTP server "listening" event.
   */

  function onListening() {
    const addr = server.address();
    const bind =
      typeof addr === "string" ? "pipe " + addr : "port " + addr.port;
    // debug("Listening on " + bind);
  }
} // End of startServer function
