"use strict";

/** @type {import('sequelize-cli').Migration} */
const { faker } = require("@faker-js/faker");

module.exports = {
  async up(queryInterface, Sequelize) {
    const startDate = "2023-01-01T00:00:00.000Z";
    const endDate = "2024-01-01T00:00:00.000Z";

    // Define the image URLs based on the provided information
    const imageUrls = {
      Updates: `${process.env.CDN_URL}/public/updates.png`,
      Interesting: `${process.env.CDN_URL}/public/interesting.png`,
      Research: `${process.env.CDN_URL}/public/research.png`,
      Learn: `${process.env.CDN_URL}/public/learn.png`,
      Podcast: `${process.env.CDN_URL}/public/podcast.png`,
    };

    const newsCategories = [];

    // Loop through the newsCategories and create seed data
    for (const [name, imageUrl] of Object.entries(imageUrls)) {
      const slug = name.toLocaleLowerCase();
      const createdAt = faker.date.between(startDate, endDate);
      const updatedAt = faker.date.between(startDate, endDate);

      newsCategories.push({
        name,
        image: imageUrl,
        slug,
        createdAt,
        updatedAt,
      });
    }

    // Insert seed data into the 'newsCategories' table
    await queryInterface.bulkInsert("newsCategories", newsCategories, {
      ignoreDuplicates: true,
    });
  },

  async down(queryInterface, Sequelize) {
    /**
     * Add commands to revert seed here.
     *
     * Example:
     * await queryInterface.bulkDelete('categories', null, {});
     */
    await queryInterface.bulkDelete("newsCategories");
  },
};
