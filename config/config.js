require("dotenv").config();
const config = {
  test: {
    username: "root",
    password: "password",
    database: "db",
    host: "localhost",
    dialect: "mysql",
    timezone: 'Z',
  },
  development: {
    username: process.env.DEVUSERNAME,
    password: process.env.DEVPASSWORD,
    database: process.env.DEVDATABASE,
    host: process.env.DEVHOST,
    schema: process.env.DEVSCHEMA || 'public',
    port: 5432,
    dialect: "postgres",
    dialectOptions: {
      ssl: {
        require: true,
        rejectUnauthorized: false,
      },
    },
    pool: {
      max: 50,
      min: 0,
      acquire: 60000,
      idle: 10000,
      evict: 10000,
      acquireTimeout: 60000,
    },
    language: "en",
    timezone: 'UTC',
    logging: process.env.log == 1 ? true : false,
  },
  production: {
    username: process.env.USERNAME,
    password: process.env.PASSWORD,
    database: process.env.DATABASE,
    host: process.env.HOST,
    schema: process.env.DEVSCHEMA || 'public',
    port: 5432,
    dialect: "postgres",
    dialectOptions: {
      ssl: {
        require: true,
        rejectUnauthorized: false,
      },
    },
    pool: {
      max: 50,
      min: 0,
      acquire: 60000,
      idle: 10000,
      evict: 10000,
      acquireTimeout: 60000,
    },
    language: "en",
    timezone: 'UTC',
    logging: process.env.log == 1 ? true : false,

  },
  developmentSQL: {
    username: process.env.DEVUSERNAME,
    password: process.env.DEVPASSWORD,
    database: process.env.DEVDATABASE,
    host: process.env.DEVHOST,
    port: 3306,
    dialect: "mysql",
    pool: {
      max: 50,
      min: 0,
      acquire: 60000,
      idle: 10000,
      evict: 10000,
      acquireTimeout: 60000,
    },
    language: "en",
    timezone: 'Z',
    logging: process.env.log == 1 ? true : false,

  },
  productionSQL: {
    username: process.env.USERNAME,
    password: process.env.PASSWORD,
    database: process.env.DATABASE,
    host: process.env.HOST,
    port: 3306,
    dialect: "mysql",
    dialectOptions: {
      ssl: "Amazon RDS",
    },
    pool: {
      max: 250,
      min: 0,
      acquire: 60000,
      idle: 10000,
      evict: 10000,
      acquireTimeout: 60000,
    },
    language: "en",
    timezone: 'Z',
    logging: process.env.log == 1 ? true : false,

  },
};

module.exports = config;
