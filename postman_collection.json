{"info": {"_postman_id": "oroi-backend-collection", "name": "Oroi Backend API", "description": "API collection for the Oroi language learning platform", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "<PERSON><PERSON>", "description": "Authentication endpoints", "item": [{"name": "Verify OTP", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"otp\": \"123456\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/v1/users/verify", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "verify"]}, "description": "Verify OTP for user registration"}}, {"name": "Sign Up", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"username\": \"testuser\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/v1/users/signup", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "signup"]}, "description": "Register a new user"}}, {"name": "Google Auth", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"token\": \"google-auth-token\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/v1/users/google", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "google"]}, "description": "Google social authentication"}}, {"name": "OAuth", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"token\": \"oauth-token\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/v1/users/oauth", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "o<PERSON>h"]}, "description": "OAuth authentication"}}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/v1/users/login", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "login"]}, "description": "User login"}}]}, {"name": "Users", "description": "User management endpoints", "item": [{"name": "Upload Image", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "image", "type": "file", "src": []}]}, "url": {"raw": "{{base_url}}/api/v1/users/upload", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "upload"]}, "description": "Upload user image"}}, {"name": "Get Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/users/me", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "me"]}, "description": "Get current user profile"}}, {"name": "Update Profile", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "username", "value": "newusername", "type": "text"}, {"key": "profilePic", "type": "file", "src": []}]}, "url": {"raw": "{{base_url}}/api/v1/users/profile", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "profile"]}, "description": "Update user profile"}}, {"name": "Update FCM", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"FCM\": \"fcm-token\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/v1/users/fcm", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "fcm"]}, "description": "Update FCM token"}}]}, {"name": "Levels", "description": "Level management endpoints", "item": [{"name": "Get All Levels", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/levels?page=1&limit=10&name=&description=", "host": ["{{base_url}}"], "path": ["api", "v1", "levels"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "name", "value": ""}, {"key": "description", "value": ""}]}, "description": "Get all levels with pagination and filtering"}}, {"name": "Get Level by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/levels/:id", "host": ["{{base_url}}"], "path": ["api", "v1", "levels", ":id"], "variable": [{"key": "id", "value": ""}]}, "description": "Get a level by ID"}}]}, {"name": "Languages", "description": "Language management endpoints", "item": [{"name": "Get All Languages", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/languages?page=1&limit=10&name=&code=", "host": ["{{base_url}}"], "path": ["api", "v1", "languages"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "name", "value": ""}, {"key": "code", "value": ""}]}, "description": "Get all languages with pagination and filtering"}}, {"name": "Get Language by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/languages/:id", "host": ["{{base_url}}"], "path": ["api", "v1", "languages", ":id"], "variable": [{"key": "id", "value": ""}]}, "description": "Get a language by ID"}}]}, {"name": "Courses", "description": "Course management endpoints", "item": [{"name": "Get All Courses", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/courses?page=1&limit=10&name=&languageId=&levelId=", "host": ["{{base_url}}"], "path": ["api", "v1", "courses"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "name", "value": ""}, {"key": "languageId", "value": ""}, {"key": "levelId", "value": ""}]}, "description": "Get all courses with pagination and filtering"}}, {"name": "Get Course by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/courses/:id", "host": ["{{base_url}}"], "path": ["api", "v1", "courses", ":id"], "variable": [{"key": "id", "value": ""}]}, "description": "Get a course by ID"}}]}, {"name": "Flashcards", "description": "Flashcard management endpoints", "item": [{"name": "Get All Flashcards", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/flashcards?page=1&limit=10&word=&languageId=", "host": ["{{base_url}}"], "path": ["api", "v1", "flashcards"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "word", "value": ""}, {"key": "languageId", "value": ""}]}, "description": "Get all flashcards with pagination and filtering"}}, {"name": "Get Flashcard by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/flashcards/:id", "host": ["{{base_url}}"], "path": ["api", "v1", "flashcards", ":id"], "variable": [{"key": "id", "value": ""}]}, "description": "Get a flashcard by ID"}}, {"name": "Like/Dislike Flashcard", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/flashcards/likedislike/:id?type=like", "host": ["{{base_url}}"], "path": ["api", "v1", "flashcards", "likedislike", ":id"], "query": [{"key": "type", "value": "like"}], "variable": [{"key": "id", "value": ""}]}, "description": "Like or dislike a flashcard"}}, {"name": "Toggle Bookmark", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/flashcards/bookmark/:id", "host": ["{{base_url}}"], "path": ["api", "v1", "flashcards", "bookmark", ":id"], "variable": [{"key": "id", "value": ""}]}, "description": "Toggle bookmark for a flashcard"}}]}, {"name": "Quizzes", "description": "Quiz management endpoints", "item": [{"name": "Get All Quizzes", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/quizzes?page=1&limit=10&name=", "host": ["{{base_url}}"], "path": ["api", "v1", "quizzes"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "name", "value": ""}]}, "description": "Get all quizzes with pagination and filtering"}}, {"name": "Get Quiz by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/quizzes/:id", "host": ["{{base_url}}"], "path": ["api", "v1", "quizzes", ":id"], "variable": [{"key": "id", "value": ""}]}, "description": "Get a quiz by ID"}}]}, {"name": "Questions", "description": "Question management endpoints", "item": [{"name": "Get All Questions", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/questions?page=1&limit=10&quizId=", "host": ["{{base_url}}"], "path": ["api", "v1", "questions"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "quizId", "value": ""}]}, "description": "Get all questions with pagination and filtering"}}, {"name": "Get Question by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/questions/:id", "host": ["{{base_url}}"], "path": ["api", "v1", "questions", ":id"], "variable": [{"key": "id", "value": ""}]}, "description": "Get a question by ID"}}]}, {"name": "Groups", "description": "Group management endpoints", "item": [{"name": "Get All Groups", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/groups?page=1&limit=10&name=&languageId=", "host": ["{{base_url}}"], "path": ["api", "v1", "groups"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "name", "value": ""}, {"key": "languageId", "value": ""}]}, "description": "Get all groups with pagination and filtering"}}, {"name": "Get Group by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/groups/:id", "host": ["{{base_url}}"], "path": ["api", "v1", "groups", ":id"], "variable": [{"key": "id", "value": ""}]}, "description": "Get a group by ID"}}]}, {"name": "Support", "description": "Support ticket management endpoints", "item": [{"name": "Get All Support Tickets", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/support?page=1&limit=10&status=", "host": ["{{base_url}}"], "path": ["api", "v1", "support"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "status", "value": ""}]}, "description": "Get all support tickets with pagination and filtering"}}, {"name": "Get Support Ticket by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/support/:id", "host": ["{{base_url}}"], "path": ["api", "v1", "support", ":id"], "variable": [{"key": "id", "value": ""}]}, "description": "Get a support ticket by ID"}}]}, {"name": "Notifications", "description": "Notification management endpoints", "item": [{"name": "Get All Notifications", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/notifications?page=1&limit=10", "host": ["{{base_url}}"], "path": ["api", "v1", "notifications"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}, "description": "Get all notifications with pagination"}}, {"name": "Get Notification by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/notifications/:id", "host": ["{{base_url}}"], "path": ["api", "v1", "notifications", ":id"], "variable": [{"key": "id", "value": ""}]}, "description": "Get a notification by ID"}}, {"name": "Record Notification Click", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"notificationId\": \"notification-uuid\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/v1/notifications/click", "host": ["{{base_url}}"], "path": ["api", "v1", "notifications", "click"]}, "description": "Record notification click"}}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "http://localhost:3000", "type": "string"}, {"key": "token", "value": "your-jwt-token", "type": "string"}]}