# Production Environment Variables for Vercel Deployment
NODE_ENV=production
PORT=3000

# JWT Configuration
JWT_SECRET=your_jwt_secret_here
JWT_EXPIREIN=7d

# Logging
log=1

# Database Configuration (PostgreSQL)
# Replace with your production database credentials
USERNAME=your_db_username
PASSWORD=your_db_password
DATABASE=your_db_name
HOST=your_db_host
DEVSCHEMA=public

# MongoDB Configuration
MONGODB_URI=your_mongodb_connection_string

# Redis Configuration
REDIS_URL=your_redis_url

# AWS S3 Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
BUCKET=your_s3_bucket_name
BUCKET_URL=https://your_s3_bucket_name.s3.amazonaws.com
CDN_URL=https://your_cdn_url

# CORS Configuration
PROD_CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
DEV_CORS_ORIGINS=http://localhost:3000,http://localhost:3001

# API Keys
API_KEY=your_production_api_key
API_KEY_DEV=your_development_api_key

# Firebase Configuration
FIREBASE_PROJECT_ID=your_firebase_project_id
FIREBASE_PRIVATE_KEY=your_firebase_private_key
FIREBASE_CLIENT_EMAIL=your_firebase_client_email

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Notification Configuration
TOPIC=your_production_topic
DEV_TOPIC=your_development_topic

# Email Configuration (if using nodemailer)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_email_password

# IP Whitelist (if needed)
PROD_ALLOWED_IPS=0.0.0.0
DEV_ALLOWED_IPS=127.0.0.1,::1

# Elasticsearch Configuration
ELASTICSEARCH_URL=your_elasticsearch_url

# MSG91 Configuration (if using)
MSG91_AUTH_KEY=your_msg91_auth_key
MSG91_TEMPLATE_ID=your_msg91_template_id

# Slack Webhook (if using)
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your/slack/webhook
