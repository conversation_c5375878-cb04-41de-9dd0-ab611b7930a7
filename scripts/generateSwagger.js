const fs = require("fs");
const path = require("path");
const { Sequelize, DataTypes } = require("sequelize");

// Function to convert Sequelize type to Swagger type
function getSwaggerType(sequelizeType) {
  if (sequelizeType instanceof DataTypes.STRING) return "string";
  if (sequelizeType instanceof DataTypes.TEXT) return "string";
  if (sequelizeType instanceof DataTypes.INTEGER) return "integer";
  if (sequelizeType instanceof DataTypes.FLOAT) return "number";
  if (sequelizeType instanceof DataTypes.BOOLEAN) return "boolean";
  if (sequelizeType instanceof DataTypes.DATE) return "string";
  if (sequelizeType instanceof DataTypes.UUID) return "string";
  if (sequelizeType instanceof DataTypes.JSON) return "object";
  return "string";
}

// Function to generate Swagger documentation for a model
function generateModelSwagger(
  modelName,
  modelPath,
  joiSchemaPath,
  controllerPath
) {
  // Read model file
  const modelContent = fs.readFileSync(modelPath, "utf8");
  const joiSchemaContent = fs.readFileSync(joiSchemaPath, "utf8");
  const controllerContent = fs.readFileSync(controllerPath, "utf8");

  // Extract model properties
  const propertiesMatch = modelContent.match(
    /define\s*\(\s*["']\w+["']\s*,\s*{([^}]+)}/
  );
  const properties = propertiesMatch ? propertiesMatch[1] : "";

  // Extract Joi schema
  const joiSchemaMatch = joiSchemaContent.match(
    /create:\s*Joi\.object\(\)\.keys\({([^}]+)}\)/
  );
  const joiSchema = joiSchemaMatch ? joiSchemaMatch[1] : "";

  // Generate Swagger documentation
  const swaggerDoc = `/**
 * @swagger
 * components:
 *   schemas:
 *     ${modelName}:
 *       type: object
 *       required:
 *         ${extractRequiredFields(joiSchema)}
 *       properties:
 *         ${generateProperties(properties, modelName)}
 *       example:
 *         ${generateExample(properties)}
 *
 *     Create${modelName}Request:
 *       type: object
 *       required:
 *         ${extractRequiredFields(joiSchema)}
 *       properties:
 *         ${generateRequestProperties(joiSchema)}
 *       example:
 *         ${generateRequestExample(joiSchema)}
 *
 *     Update${modelName}Request:
 *       type: object
 *       properties:
 *         ${generateRequestProperties(joiSchema)}
 *       example:
 *         ${generateRequestExample(joiSchema)}
 *
 *   parameters:
 *     ${modelName.toLowerCase()}Id:
 *       in: path
 *       name: id
 *       required: true
 *       schema:
 *         type: string
 *         format: uuid
 *       description: The ${modelName} ID
 *
 *   responses:
 *     UnauthorizedError:
 *       description: Access token is missing or invalid
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: integer
 *                 example: 401
 *               message:
 *                 type: string
 *                 example: Unauthorized attempt, login again!
 *
 * paths:
 *   /api/v1/${modelName.toLowerCase()}:
 *     get:
 *       summary: Get all ${modelName.toLowerCase()}s
 *       tags: [${modelName}s]
 *       parameters:
 *         - in: query
 *           name: page
 *           schema:
 *             type: integer
 *             default: 1
 *           description: Page number for pagination
 *         - in: query
 *           name: limit
 *           schema:
 *             type: integer
 *             default: 10
 *           description: Number of items per page
 *       responses:
 *         200:
 *           description: List of ${modelName.toLowerCase()}s
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: ${modelName}s retrieved successfully
 *                   data:
 *                     type: object
 *                     properties:
 *                       ${modelName.toLowerCase()}s:
 *                         type: array
 *                         items:
 *                           $ref: '#/components/schemas/${modelName}'
 *                       pagination:
 *                         type: object
 *                         properties:
 *                           total:
 *                             type: integer
 *                             description: Total number of ${modelName.toLowerCase()}s
 *                           page:
 *                             type: integer
 *                             description: Current page number
 *                           limit:
 *                             type: integer
 *                             description: Number of items per page
 *                           pages:
 *                             type: integer
 *                             description: Total number of pages
 *
 *   /api/v1/${modelName.toLowerCase()}/{id}:
 *     get:
 *       summary: Get ${modelName.toLowerCase()} by ID
 *       tags: [${modelName}s]
 *       parameters:
 *         - $ref: '#/components/parameters/${modelName.toLowerCase()}Id'
 *       responses:
 *         200:
 *           description: ${modelName} retrieved successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: ${modelName} retrieved successfully
 *                   data:
 *                     $ref: '#/components/schemas/${modelName}'
 *         404:
 *           description: ${modelName} not found
 *
 *   /api/v1/admin/${modelName.toLowerCase()}:
 *     post:
 *       summary: Create a new ${modelName.toLowerCase()}
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/Create${modelName}Request'
 *       responses:
 *         201:
 *           description: ${modelName} created successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 201
 *                   message:
 *                     type: string
 *                     example: ${modelName} created successfully
 *                   data:
 *                     $ref: '#/components/schemas/${modelName}'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *
 *   /api/v1/admin/${modelName.toLowerCase()}/{id}:
 *     patch:
 *       summary: Update a ${modelName.toLowerCase()}
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/${modelName.toLowerCase()}Id'
 *       requestBody:
 *         required: true
 *         content:
 *           multipart/form-data:
 *             schema:
 *               $ref: '#/components/schemas/Update${modelName}Request'
 *       responses:
 *         200:
 *           description: ${modelName} updated successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: ${modelName} updated successfully
 *                   data:
 *                     $ref: '#/components/schemas/${modelName}'
 *         400:
 *           description: Invalid input data
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: ${modelName} not found
 *
 *     delete:
 *       summary: Delete a ${modelName.toLowerCase()}
 *       tags: [Admin]
 *       security:
 *         - bearerAuth: []
 *       parameters:
 *         - $ref: '#/components/parameters/${modelName.toLowerCase()}Id'
 *       responses:
 *         200:
 *           description: ${modelName} deleted successfully
 *           content:
 *             application/json:
 *               schema:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: integer
 *                     example: 200
 *                   message:
 *                     type: string
 *                     example: ${modelName} deleted successfully
 *         401:
 *           $ref: '#/components/responses/UnauthorizedError'
 *         403:
 *           description: Forbidden - User does not have admin privileges
 *         404:
 *           description: ${modelName} not found
 */`;

  return swaggerDoc;
}

// Helper functions
function extractRequiredFields(joiSchema) {
  const requiredFields = joiSchema.match(/\.required\(\)/g) || [];
  return requiredFields.map((field) => `- ${field}`).join("\n         ");
}

function generateProperties(properties, modelName) {
  // Implementation to generate properties from model definition
  return properties
    .split(",")
    .map((prop) => {
      const [name, type] = prop.split(":").map((s) => s.trim());
      return `${name}:
           type: ${getSwaggerType(type)}
           description: The ${name} of the ${modelName}`;
    })
    .join("\n         ");
}

function generateExample(properties) {
  // Implementation to generate example values
  return properties
    .split(",")
    .map((prop) => {
      const name = prop.split(":")[0].trim();
      return `${name}: "Example ${name}"`;
    })
    .join("\n         ");
}

function generateRequestProperties(joiSchema) {
  // Implementation to generate request properties from Joi schema
  return joiSchema
    .split(",")
    .map((prop) => {
      const [name, type] = prop.split(":").map((s) => s.trim());
      return `${name}:
           type: ${getSwaggerType(type)}`;
    })
    .join("\n         ");
}

function generateRequestExample(joiSchema) {
  // Implementation to generate request example values
  return joiSchema
    .split(",")
    .map((prop) => {
      const name = prop.split(":")[0].trim();
      return `${name}: "Example ${name}"`;
    })
    .join("\n         ");
}

// Main function to generate documentation for all models
function generateAllSwaggerDocs() {
  const modulesDir = path.join(__dirname, "../modules");
  const modules = fs.readdirSync(modulesDir);

  modules.forEach((module) => {
    const modelPath = path.join(modulesDir, module, "model.js");
    const joiSchemaPath = path.join(modulesDir, module, "joiSchema.js");
    const controllerPath = path.join(modulesDir, module, "controller.js");

    if (
      fs.existsSync(modelPath) &&
      fs.existsSync(joiSchemaPath) &&
      fs.existsSync(controllerPath)
    ) {
      const swaggerDoc = generateModelSwagger(
        module,
        modelPath,
        joiSchemaPath,
        controllerPath
      );
      const swaggerPath = path.join(modulesDir, module, "swagger.js");
      fs.writeFileSync(swaggerPath, swaggerDoc);
      console.log(`Generated Swagger documentation for ${module}`);
    }
  });
}

// Run the script
generateAllSwaggerDocs();
