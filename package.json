{"name": "oroi", "version": "0.0", "description": "Oroi - Descirption", "main": "app.js", "private": true, "author": "Milan Golakiya <<EMAIL>>", "license": "ISC", "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/"}, "homepage": "https://oroi.com", "keywords": ["Oroi", "Words", "Learning"], "maintainers": [{"name": "Milan <PERSON>", "email": "<EMAIL>", "signature": "MG"}], "scripts": {"lint": "eslint . --fix", "start": "node --max-old-space-size=4096 ./bin/www", "startlint": "npm run lint && node --max-old-space-size=4096 ./bin/www", "dev": "nodemon --max-old-space-size=4096 ./bin/www", "devlint": "npm run lint && nodemon --max-old-space-size=4096 ./bin/www", "build": "echo 'Build completed'", "vercel-build": "echo 'Vercel build completed'", "migration:run": "npx sequelize-cli db:migrate", "migration:undo": "npx sequelize-cli db:migrate:undo", "migration:undo:all": "npx sequelize-cli db:migrate:undo:all", "migration:create": "npx sequelize-cli migration:generate --name", "seeder:create": "npx sequelize-cli seed:generate --name", "seeder:run": "npx sequelize-cli db:seed --seed", "seeder:all": "npx sequelize-cli db:seed:all", "seeder:undo": "npx sequelize-cli db:seed:undo", "generate-swagger": "node scripts/generateSwagger.js"}, "dependencies": {"@aws-sdk/client-s3": "^3.438.0", "@elastic/elasticsearch": "^8.12.2", "@faker-js/faker": "^8.3.1", "axios": "^1.6.0", "bcryptjs": "^2.4.3", "cloudinary": "^2.4.0", "compression": "^1.7.4", "cookie-parser": "~1.4.6", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.18.2", "express-rate-limit": "^7.1.3", "firebase-admin": "^11.11.0", "google-auth-library": "^9.4.2", "helmet": "^7.0.0", "http-errors": "^2.0.0", "joi": "^17.11.0", "joi-to-json": "^4.3.1", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "mongoose": "^8.13.2", "multer": "^1.4.5-lts.1", "multer-s3": "^3.0.1", "multer-storage-cloudinary": "^4.0.0", "mysql2": "^3.6.2", "nodemailer": "^6.9.7", "openai": "^4.14.1", "pg": "^8.12.0", "pg-hstore": "^2.3.4", "rate-limit-redis": "^4.1.2", "redis": "^4.6.10", "route-snapshot": "^1.0.0", "sequelize": "^6.37.3", "sharp": "^0.32.6", "slugify": "^1.6.6", "string-similarity": "^4.0.4", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^9.0.1", "xmlbuilder": "^15.1.1"}, "devDependencies": {"@eslint/js": "^9.2.0", "eslint": "^9.2.0", "globals": "^15.2.0"}}