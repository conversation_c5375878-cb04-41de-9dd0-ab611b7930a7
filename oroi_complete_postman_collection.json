{"info": {"_postman_id": "oroi-complete-collection", "name": "Oroi Backend API - Complete Collection", "description": "Complete API collection for the Oroi language learning platform with all endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "version": "2.0.0"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "item": [{"name": "Authentication", "description": "User authentication endpoints", "item": [{"name": "User Signup", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"testuser\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/v1/user/signup", "host": ["{{base_url}}"], "path": ["api", "v1", "user", "signup"]}, "description": "Register a new user account"}}, {"name": "Verify OTP", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"otp\": \"123456\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/v1/user/verify", "host": ["{{base_url}}"], "path": ["api", "v1", "user", "verify"]}, "description": "Verify OTP for user registration"}}, {"name": "User Login", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/v1/user/login", "host": ["{{base_url}}"], "path": ["api", "v1", "user", "login"]}, "description": "Login with email and password", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.token) {", "        pm.collectionVariables.set('token', response.token);", "    }", "}"], "type": "text/javascript"}}]}}, {"name": "Google OAuth", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"idToken\": \"google_id_token_here\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/v1/user/oauth", "host": ["{{base_url}}"], "path": ["api", "v1", "user", "o<PERSON>h"]}, "description": "Login with Google OAuth"}}, {"name": "Google Social Auth", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"idToken\": \"google_id_token_here\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/v1/user/google", "host": ["{{base_url}}"], "path": ["api", "v1", "user", "google"]}, "description": "Social authentication with Google"}}]}, {"name": "Admin Au<PERSON>ntication", "description": "Admin authentication endpoints", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"admin123\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/v1/admin/user/login", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "user", "login"]}, "description": "Admin login", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.token) {", "        pm.collectionVariables.set('admin_token', response.token);", "    }", "}"], "type": "text/javascript"}}]}}, {"name": "Create Admin", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"admin123\",\n    \"username\": \"admin\",\n    \"role\": \"Admin\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/v1/admin/user/signup", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "user", "signup"]}, "description": "Create new admin user"}}]}, {"name": "User Profile", "description": "User profile management endpoints", "item": [{"name": "Get User Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/user/me", "host": ["{{base_url}}"], "path": ["api", "v1", "user", "me"]}, "description": "Get current user profile"}}, {"name": "Update User Profile", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "username", "value": "updated_username", "type": "text"}, {"key": "profilePic", "type": "file", "src": []}]}, "url": {"raw": "{{base_url}}/api/v1/user/profile", "host": ["{{base_url}}"], "path": ["api", "v1", "user", "profile"]}, "description": "Update user profile with optional profile picture"}}, {"name": "Update FCM Token", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"FCM\": \"fcm_token_here\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/v1/user/fcm", "host": ["{{base_url}}"], "path": ["api", "v1", "user", "fcm"]}, "description": "Update Firebase Cloud Messaging token"}}, {"name": "Upload Image", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "image", "type": "file", "src": []}]}, "url": {"raw": "{{base_url}}/api/v1/user/upload", "host": ["{{base_url}}"], "path": ["api", "v1", "user", "upload"]}, "description": "Upload an image file"}}]}, {"name": "App Configuration", "description": "Application configuration endpoints", "item": [{"name": "Get App Config", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/appConfig", "host": ["{{base_url}}"], "path": ["api", "v1", "appConfig"]}, "description": "Get application configuration"}}]}, {"name": "Languages", "description": "Language management endpoints", "item": [{"name": "Get All Languages", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/language", "host": ["{{base_url}}"], "path": ["api", "v1", "language"]}, "description": "Get all available languages"}}, {"name": "Get Language by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/language/{{language_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "language", "{{language_id}}"]}, "description": "Get specific language by ID"}}]}, {"name": "Flashcards", "description": "Flashcard management endpoints", "item": [{"name": "Get All Flashcards", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/flashcard", "host": ["{{base_url}}"], "path": ["api", "v1", "flashcard"]}, "description": "Get all flashcards"}}, {"name": "Get Flashcard by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/flashcard/{{flashcard_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "flashcard", "{{flashcard_id}}"]}, "description": "Get specific flashcard by ID"}}, {"name": "Get Flashcard Like/Dislike Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/flashcard/likedislike/{{flashcard_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "flashcard", "likedislike", "{{flashcard_id}}"]}, "description": "Get like/dislike status for a flashcard"}}, {"name": "Get Flashcard Bookmark Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/flashcard/bookmark/{{flashcard_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "flashcard", "bookmark", "{{flashcard_id}}"]}, "description": "Get bookmark status for a flashcard"}}]}, {"name": "Flashcard Interactions", "description": "Flashcard like, dislike, and bookmark endpoints", "item": [{"name": "Get All Flashcard Bookmarks", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/flashcardbookmark", "host": ["{{base_url}}"], "path": ["api", "v1", "flashcardbookmark"]}, "description": "Get all flashcard bookmarks"}}, {"name": "Get Flashcard Bookmark by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/flashcardbookmark/{{bookmark_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "flashcardbookmark", "{{bookmark_id}}"]}, "description": "Get specific flashcard bookmark by ID"}}, {"name": "Get All Flashcard Likes", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/flashcardlike", "host": ["{{base_url}}"], "path": ["api", "v1", "flashcardlike"]}, "description": "Get all flashcard likes"}}, {"name": "Get Flashcard Like by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/flashcardlike/{{like_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "flashcardlike", "{{like_id}}"]}, "description": "Get specific flashcard like by ID"}}, {"name": "Get All Flashcard Dislikes", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/flashcarddislike", "host": ["{{base_url}}"], "path": ["api", "v1", "flashcarddislike"]}, "description": "Get all flashcard dislikes"}}, {"name": "Get Flashcard Dislike by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/flashcarddislike/{{dislike_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "flashcarddislike", "{{dislike_id}}"]}, "description": "Get specific flashcard dislike by ID"}}]}, {"name": "Courses", "description": "Course management endpoints", "item": [{"name": "Get All Courses", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/course", "host": ["{{base_url}}"], "path": ["api", "v1", "course"]}, "description": "Get all courses"}}, {"name": "Get Course by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/course/{{course_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "course", "{{course_id}}"]}, "description": "Get specific course by ID"}}]}, {"name": "Course Flashcards", "description": "Course flashcard endpoints", "item": [{"name": "Get All Course Flashcards", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/courseflashcard", "host": ["{{base_url}}"], "path": ["api", "v1", "courseflashcard"]}, "description": "Get all course flashcards"}}, {"name": "Get Course Flashcard by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/courseflashcard/{{courseflashcard_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "courseflashcard", "{{courseflashcard_id}}"]}, "description": "Get specific course flashcard by ID"}}]}, {"name": "Course Quizzes", "description": "Course quiz endpoints", "item": [{"name": "Get All Course Quizzes", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/coursequiz", "host": ["{{base_url}}"], "path": ["api", "v1", "coursequiz"]}, "description": "Get all course quizzes"}}, {"name": "Get Course Quiz by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/coursequiz/{{coursequiz_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "coursequiz", "{{coursequiz_id}}"]}, "description": "Get specific course quiz by ID"}}]}, {"name": "Groups", "description": "Group management endpoints", "item": [{"name": "Get All Groups", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/group", "host": ["{{base_url}}"], "path": ["api", "v1", "group"]}, "description": "Get all groups"}}, {"name": "Get Group by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/group/{{group_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "group", "{{group_id}}"]}, "description": "Get specific group by ID"}}]}, {"name": "Group Flashcards", "description": "Group flashcard endpoints", "item": [{"name": "Get All Group Flashcards", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/groupflashcard", "host": ["{{base_url}}"], "path": ["api", "v1", "groupflashcard"]}, "description": "Get all group flashcards"}}, {"name": "Get Group Flashcard by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/groupflashcard/{{groupflashcard_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "groupflashcard", "{{groupflashcard_id}}"]}, "description": "Get specific group flashcard by ID"}}]}, {"name": "Quizzes", "description": "Quiz management endpoints", "item": [{"name": "Get All Quizzes", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/quiz", "host": ["{{base_url}}"], "path": ["api", "v1", "quiz"]}, "description": "Get all quizzes"}}, {"name": "Get Quiz by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/quiz/{{quiz_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "quiz", "{{quiz_id}}"]}, "description": "Get specific quiz by ID"}}]}, {"name": "Levels", "description": "Level management endpoints", "item": [{"name": "Get All Levels", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/level", "host": ["{{base_url}}"], "path": ["api", "v1", "level"]}, "description": "Get all levels"}}, {"name": "Get Level by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/level/{{level_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "level", "{{level_id}}"]}, "description": "Get specific level by ID"}}]}, {"name": "Notifications", "description": "Notification endpoints", "item": [{"name": "Get All Notifications", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/notification", "host": ["{{base_url}}"], "path": ["api", "v1", "notification"]}, "description": "Get all notifications"}}, {"name": "Get Notification by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/notification/{{notification_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "notification", "{{notification_id}}"]}, "description": "Get specific notification by ID"}}, {"name": "Click Notification", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"notificationId\": \"{{notification_id}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/v1/notification/click", "host": ["{{base_url}}"], "path": ["api", "v1", "notification", "click"]}, "description": "Mark notification as clicked"}}]}, {"name": "User Relations", "description": "User relation endpoints (user flashcards, groups, courses, languages)", "item": [{"name": "Get All User Flashcards", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/userflashcard", "host": ["{{base_url}}"], "path": ["api", "v1", "userflashcard"]}, "description": "Get all user flashcards"}}, {"name": "Get User Flashcard by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/userflashcard/{{userflashcard_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "userflashcard", "{{userflashcard_id}}"]}, "description": "Get specific user flashcard by ID"}}, {"name": "Get All User Groups", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/usergroup", "host": ["{{base_url}}"], "path": ["api", "v1", "usergroup"]}, "description": "Get all user groups"}}, {"name": "Get User Group by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/usergroup/{{usergroup_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "usergroup", "{{usergroup_id}}"]}, "description": "Get specific user group by ID"}}]}, {"name": "Admin - User Management", "description": "Admin endpoints for user management", "item": [{"name": "Get All Users (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/user", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "user"]}, "description": "Get all users (admin only)"}}, {"name": "Get User by ID (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/user/{{user_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "user", "{{user_id}}"]}, "description": "Get specific user by ID (admin only)"}}, {"name": "Block/Unblock User (Admin)", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"isBlocked\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/v1/admin/user/{{user_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "user", "{{user_id}}"]}, "description": "Block or unblock a user (admin only)"}}, {"name": "Delete User (Admin)", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/user/{{user_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "user", "{{user_id}}"]}, "description": "Delete a user (admin only)"}}]}, {"name": "Admin - Dashboard", "description": "Admin dashboard endpoints", "item": [{"name": "Get Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/dashboard/analytics", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "dashboard", "analytics"]}, "description": "Get dashboard analytics"}}, {"name": "Get Daily Data", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/dashboard/getDailyData", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "dashboard", "getDailyData"]}, "description": "Get daily dashboard data"}}, {"name": "Get Language Specific Data", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/dashboard/langugespecific", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "dashboard", "langugespecific"]}, "description": "Get language specific dashboard data"}}, {"name": "Get Top Like/Dislike Data", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/dashboard/toplikedislike", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "dashboard", "toplikedislike"]}, "description": "Get top like/dislike dashboard data"}}]}, {"name": "Admin - Content Management", "description": "Admin endpoints for content management", "item": [{"name": "Create Language (Admin)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Spanish\",\n    \"code\": \"es\",\n    \"englishName\": \"Spanish\",\n    \"isActive\": true,\n    \"isPremium\": false,\n    \"pronunciation\": \"es-pah-nyol\",\n    \"flag\": \"🇪🇸\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/v1/admin/language", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "language"]}, "description": "Create a new language (admin only)"}}, {"name": "Create Flashcard (Admin)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"word\": \"hello\",\n    \"example\": \"Hello, how are you?\",\n    \"meaning\": \"A greeting\",\n    \"pronunciation\": \"heh-loh\",\n    \"isActive\": true,\n    \"languageId\": \"{{language_id}}\",\n    \"similarWords\": [\"hi\", \"hey\"]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/v1/admin/flashcard", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "flashcard"]}, "description": "Create a new flashcard (admin only)"}}]}], "variable": [{"key": "base_url", "value": "http://localhost:3000", "type": "string"}, {"key": "token", "value": "", "type": "string"}, {"key": "admin_token", "value": "", "type": "string"}, {"key": "user_id", "value": "", "type": "string"}, {"key": "language_id", "value": "", "type": "string"}, {"key": "flashcard_id", "value": "", "type": "string"}, {"key": "course_id", "value": "", "type": "string"}, {"key": "group_id", "value": "", "type": "string"}, {"key": "quiz_id", "value": "", "type": "string"}, {"key": "level_id", "value": "", "type": "string"}, {"key": "notification_id", "value": "", "type": "string"}]}