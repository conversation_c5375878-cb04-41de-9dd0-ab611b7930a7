const mongoose = require("mongoose");

const apiLogSchema = new mongoose.Schema(
  {
    timestamp: {
      type: Date,
      default: Date.now,
    },
    method: {
      type: String,
      required: true,
    },
    path: {
      type: String,
      required: true,
    },
    userId: {
      type: String,
      required: false,
    },
    userEmail: {
      type: String,
      required: false,
    },
    payload: {
      type: mongoose.Schema.Types.Mixed,
      required: false,
    },
    jwtUser: {
      type: mongoose.Schema.Types.Mixed,
      required: false,
    },
    response: {
      type: mongoose.Schema.Types.Mixed,
      required: false,
    },
    statusCode: {
      type: Number,
      required: true,
    },
    ipAddress: {
      type: String,
      required: true,
    },
    userAgent: {
      type: String,
      required: false,
    },
    duration: {
      type: Number,
      required: false,
    },
  },
  {
    timestamps: true,
  }
);

module.exports = mongoose.model("ApiLog", apiLogSchema);
