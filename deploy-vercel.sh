#!/bin/bash

# Vercel Deployment Script for Oroi Backend
# This script helps you deploy your backend to Vercel

echo "🚀 Oroi Backend - Vercel Deployment Script"
echo "=========================================="

# Check if Vercel CLI is installed
if ! command -v vercel &> /dev/null; then
    echo "❌ Vercel CLI is not installed."
    echo "📦 Installing Vercel CLI..."
    npm install -g vercel
fi

# Check if user is logged in to Vercel
echo "🔐 Checking Vercel authentication..."
if ! vercel whoami &> /dev/null; then
    echo "🔑 Please log in to Vercel:"
    vercel login
fi

# Lint the code before deployment
echo "🔍 Running linter..."
npm run lint

if [ $? -ne 0 ]; then
    echo "❌ Linting failed. Please fix the issues before deploying."
    exit 1
fi

echo "✅ Linting passed!"

# Ask for deployment type
echo ""
echo "🎯 Select deployment type:"
echo "1) Preview deployment (for testing)"
echo "2) Production deployment"
read -p "Enter your choice (1 or 2): " choice

case $choice in
    1)
        echo "🔄 Deploying to preview environment..."
        vercel
        ;;
    2)
        echo "🔄 Deploying to production..."
        vercel --prod
        ;;
    *)
        echo "❌ Invalid choice. Exiting."
        exit 1
        ;;
esac

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 Deployment successful!"
    echo ""
    echo "📋 Next steps:"
    echo "1. Test your API endpoints"
    echo "2. Check the health endpoint: /api/v1/appConfig/health"
    echo "3. Verify API documentation: /api-docs"
    echo "4. Monitor logs: vercel logs"
    echo ""
    echo "🔗 Useful commands:"
    echo "   vercel logs --follow    # View real-time logs"
    echo "   vercel domains          # Manage custom domains"
    echo "   vercel env              # Manage environment variables"
    echo ""
else
    echo "❌ Deployment failed. Check the error messages above."
    exit 1
fi
