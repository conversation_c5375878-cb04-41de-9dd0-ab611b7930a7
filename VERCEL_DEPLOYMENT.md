# Vercel Deployment Guide for Oroi Backend

This guide will help you deploy your Node.js Express backend to Vercel.

## Prerequisites

1. **Vercel Account**: Sign up at [vercel.com](https://vercel.com)
2. **Vercel CLI**: Install globally with `npm i -g vercel`
3. **Git Repository**: Your code should be in a Git repository (GitHub, GitLab, or Bitbucket)

## Configuration Files

The following files have been configured for Vercel deployment:

### 1. `vercel.json`
- Configures Vercel build settings
- Routes all API requests to your Express app
- Sets up serverless function configuration
- Specifies deployment region (iad1 - US East)

### 2. `bin/www` (Modified)
- Added Vercel compatibility check
- Exports the Express app when running on Vercel
- Maintains local development server functionality

### 3. `.env.production`
- Template for production environment variables
- Copy this to your Vercel environment variables

## Deployment Steps

### Step 1: Prepare Your Database

**For PostgreSQL:**
```bash
# Set up your production database (recommended services):
# - Supabase (https://supabase.com)
# - Railway (https://railway.app)
# - PlanetScale (https://planetscale.com)
# - AWS RDS
# - Google Cloud SQL
```

**For MongoDB:**
```bash
# Set up MongoDB Atlas (https://www.mongodb.com/atlas)
# Get your connection string
```

**For Redis:**
```bash
# Set up Redis (recommended services):
# - Upstash (https://upstash.com)
# - Redis Labs (https://redislabs.com)
# - Railway Redis
```

### Step 2: Set Up External Services

1. **AWS S3** (for file storage):
   - Create an S3 bucket
   - Set up IAM user with S3 permissions
   - Get access keys

2. **Firebase** (for authentication):
   - Set up Firebase project
   - Get service account credentials

3. **Email Service** (for notifications):
   - Configure SMTP settings (Gmail, SendGrid, etc.)

### Step 3: Deploy to Vercel

#### Option A: Deploy via Vercel Dashboard

1. Go to [vercel.com/dashboard](https://vercel.com/dashboard)
2. Click "New Project"
3. Import your Git repository
4. Vercel will auto-detect the configuration
5. Add environment variables (see Step 4)
6. Deploy

#### Option B: Deploy via CLI

```bash
# Login to Vercel
vercel login

# Deploy from your project directory
vercel

# Follow the prompts:
# - Set up and deploy? Y
# - Which scope? (select your account)
# - Link to existing project? N
# - Project name: oroi-backend
# - Directory: ./
# - Override settings? N

# For production deployment
vercel --prod
```

### Step 4: Configure Environment Variables

In your Vercel dashboard, go to Project Settings > Environment Variables and add:

#### Required Variables:
```
NODE_ENV=production
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIREIN=7d

# Database
USERNAME=your_db_username
PASSWORD=your_db_password
DATABASE=your_db_name
HOST=your_db_host
DEVSCHEMA=public

# MongoDB
MONGODB_URI=mongodb+srv://username:<EMAIL>/database

# Redis
REDIS_URL=redis://username:password@host:port

# AWS S3
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
BUCKET=your_s3_bucket_name
BUCKET_URL=https://your_s3_bucket_name.s3.amazonaws.com

# CORS
PROD_CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Firebase
FIREBASE_PROJECT_ID=your_firebase_project_id
FIREBASE_PRIVATE_KEY=your_firebase_private_key
FIREBASE_CLIENT_EMAIL=your_firebase_client_email
```

#### Optional Variables:
```
API_KEY=your_production_api_key
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_email_password
ELASTICSEARCH_URL=your_elasticsearch_url
SLACK_WEBHOOK_URL=your_slack_webhook_url
```

### Step 5: Database Migration

After deployment, run database migrations:

```bash
# If using a migration service, run:
# This depends on your database setup

# For Sequelize migrations (if your DB supports it):
# You might need to run this from a local environment
# connected to your production database
npm run migration:run
```

### Step 6: Test Your Deployment

1. **Health Check**: Visit `https://your-app.vercel.app/api/v1/appConfig`
2. **API Documentation**: Visit `https://your-app.vercel.app/api-docs`
3. **Test Authentication**: Try the signup/login endpoints

## Important Notes

### Serverless Limitations

1. **Function Timeout**: Maximum 30 seconds (configured in vercel.json)
2. **Memory Limit**: 1024MB for Pro plans, 512MB for Hobby
3. **Cold Starts**: First request after inactivity may be slower
4. **File System**: Read-only except for `/tmp` directory

### Database Connections

- Use connection pooling for better performance
- Consider using serverless-friendly databases
- Implement proper connection cleanup

### File Uploads

- Files are uploaded to AWS S3, not stored locally
- Temporary files in `/tmp` are cleaned up after function execution

### Monitoring

- Use Vercel Analytics for performance monitoring
- Set up error tracking (Sentry, LogRocket, etc.)
- Monitor database connections and performance

## Troubleshooting

### Common Issues:

1. **Environment Variables**: Ensure all required variables are set
2. **Database Connections**: Check connection strings and credentials
3. **CORS Issues**: Verify CORS origins are correctly configured
4. **Function Timeout**: Optimize slow database queries
5. **Memory Issues**: Reduce memory usage or upgrade Vercel plan

### Debugging:

```bash
# View deployment logs
vercel logs your-deployment-url

# View function logs in real-time
vercel logs --follow
```

### Local Testing:

```bash
# Test locally with Vercel environment
vercel dev

# This will:
# - Start local development server
# - Use Vercel's serverless function simulation
# - Load environment variables from Vercel
```

## Custom Domain

1. Go to Project Settings > Domains
2. Add your custom domain
3. Configure DNS records as instructed
4. Update CORS origins to include your custom domain

## Scaling Considerations

- **Database**: Use read replicas for better performance
- **Caching**: Implement Redis caching for frequently accessed data
- **CDN**: Use Vercel's Edge Network for static assets
- **Rate Limiting**: Configure appropriate rate limits for your API

Your Oroi backend is now ready for production on Vercel! 🚀
