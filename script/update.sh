#!/bin/bash

# Set appropriate permissions for the application directory (replace with your directory)
sudo chmod -R 777 /root/oroi-backend

# Grant execute permissions to the script
chmod +x update.sh

# Change to the application directory
cd /root/oroi-backend

# Update the codebase from your repository
git restore .
git pull

# Install dependencies
npm install

# Restart the application using PM2 (replace with your app name)
pm2 restart "oroi-backend"
