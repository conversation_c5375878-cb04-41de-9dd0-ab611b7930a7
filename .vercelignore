# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment files
.env
.env.local
.env.development
.env.test
example.env

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
.nyc_output/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Development and testing files
test/
tests/
__tests__/
*.test.js
*.spec.js

# Documentation (optional - remove if you want docs deployed)
docs/
README.md
VERCEL_DEPLOYMENT.md

# Git files
.git/
.gitignore

# Deployment scripts
deploy.sh
script/

# Temporary files
tmp/
temp/

# Database files (if any local db files)
*.sqlite
*.db

# Backup files
*.bak
*.backup

# Lock files (keep package-lock.json but ignore others)
yarn.lock

# ESLint
.eslintrc.*
lint.js

# Migration files (optional - include if you need them)
# migrations/
# seeders/
