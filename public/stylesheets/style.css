body {
  margin: 0;
  font-family: Arial, sans-serif;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: #f3f3f3;
}

.loader {
  border: 5px solid #66ccff;
  border-top: 5px solid #003366;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 2s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
.logo-image {
  mix-blend-mode: multiply;
  height: 400px;
  animation: smoke-bounce 2s ease-in-out infinite;
}

@keyframes smoke-bounce {
  0%, 100% {
    opacity: 0.1;
    transform: translateY(0);
    filter: blur(4px);
  }
  20% {
    opacity: 0.3;
    transform: translateY(-30px);
    filter: blur(3px);
  }
  40% {
    opacity: 1;
    transform: translateY(-60px);
    filter: blur(0px);
  }
  60% {
    transform: translateY(-400px);
    opacity: 0;
    filter: blur(3px);
  }
  80% {
    opacity: 0;
    transform: translateY(400px);
    filter: blur(4px);
  }
  90% {
    opacity: 0.1;
    transform: translateY(100px);
    filter: blur(4px);
  }
}

@keyframes bounce {
  0% {
    opacity: 0.3;
    transform: translateY(-100%);
  }
  80% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0.3;
    transform: translateY(-100%);
  }
}
.text {
  text-align: center;
  color: #333;
}

h1 {
  font-size: 32px;
  margin-bottom: 10px;
}

a.oroi-link {
  color: #003366;
  text-decoration: none;
}

a.oroi-link:hover {
  text-decoration: underline;
}

a.sangam-link {
  color: #00B7FF;
  text-decoration: none;
}

a.sangam-link:hover {
  text-decoration: underline;
}
